<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjCertMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjCertPO">
    <!--@mbg.generated-->
    <!--@Table rv_authprj_cert-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="authprj_id" property="authprjId" />
    <result column="cert_temp_id" property="certTempId" />
    <result column="obtain_type" property="obtainType" />
    <result column="obtain_value" property="obtainValue" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, authprj_id, cert_temp_id, obtain_type, obtain_value, deleted, create_user_id, 
    create_time, update_user_id, update_time
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_authprj_cert
    (id, org_id, authprj_id, cert_temp_id, obtain_type, obtain_value, deleted, create_user_id, 
      create_time, update_user_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.authprjId}, #{item.certTempId}, #{item.obtainType}, 
        #{item.obtainValue}, #{item.deleted}, #{item.createUserId}, #{item.createTime}, 
        #{item.updateUserId}, #{item.updateTime})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    authprj_id=values(authprj_id),
    cert_temp_id=values(cert_temp_id),
    obtain_type=values(obtain_type),
    obtain_value=values(obtain_value),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time)
  </insert>

  <!-- 证书列表查询结果映射 -->
  <resultMap id="CertVOResultMap" type="com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertVO">
    <id column="id" property="id" />
    <result column="cert_temp_id" property="certTempId" />
    <result column="obtain_type" property="obtainType" />
    <result column="obtain_value" property="obtainValue" />
    <result column="cert_temp_name" property="certTempName" />
    <result column="cert_cover_url" property="certCoverUrl" />
    <result column="valid_month" property="validMonth" />
  </resultMap>

  <!-- 证书颁发记录查询结果映射 -->
  <resultMap id="CertIssueVOResultMap" type="com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertIssueVO">
    <result column="issue_id" property="issueId" />
    <result column="issue_no" property="issueNo" />
    <result column="user_id" property="userId" />
    <result column="full_name" property="fullName" />
    <result column="user_name" property="userName" />
    <result column="dept_name" property="deptName" />
    <result column="position_name" property="positionName" />
    <result column="issue_time" property="issueTime" />
    <result column="expired_time" property="expiredTime" />
    <result column="status" property="status" />
  </resultMap>

  <!-- 根据认证项目ID查询证书列表（包含证书模板信息） -->
  <select id="selectCertListByAuthprjId" resultMap="CertVOResultMap">
    SELECT
      c.id,
      c.cert_temp_id,
      c.obtain_type,
      c.obtain_value,
      '' as cert_temp_name,
      '' as cert_cover_url,
      0 as valid_month
    FROM rv_authprj_cert c
    WHERE c.org_id = #{orgId}
      AND c.authprj_id = #{authprjId}
      AND c.deleted = 0
    ORDER BY c.create_time DESC
  </select>

</mapper>