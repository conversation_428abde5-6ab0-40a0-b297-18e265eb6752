<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjCertObtainItemMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjCertObtainItemPO">
    <!--@mbg.generated-->
    <!--@Table rv_authprj_cert_obtain_item-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="authprj_cert_id" property="authprjCertId" />
    <result column="authprj_id" property="authprjId" />
    <result column="cert_temp_id" property="certTempId" />
    <result column="act_id" property="actId" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, authprj_cert_id, authprj_id, cert_temp_id, act_id, deleted, create_user_id, 
    create_time, update_user_id, update_time
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_authprj_cert_obtain_item
    (id, org_id, authprj_cert_id, authprj_id, cert_temp_id, act_id, deleted, create_user_id, 
      create_time, update_user_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.authprjCertId}, #{item.authprjId}, #{item.certTempId}, 
        #{item.actId}, #{item.deleted}, #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, 
        #{item.updateTime})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    authprj_cert_id=values(authprj_cert_id),
    authprj_id=values(authprj_id),
    cert_temp_id=values(cert_temp_id),
    act_id=values(act_id),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time)
  </insert>

  <!-- 根据证书ID删除获取条件配置 -->
  <update id="deleteByAuthprjCertId">
    UPDATE rv_authprj_cert_obtain_item
    SET deleted = 1, update_time = NOW(), update_user_id = #{operator}
    WHERE org_id = #{orgId}
      AND authprj_cert_id = #{authprjCertId}
      AND deleted = 0
  </update>

  <select id="selectObtainItemsByCertId" resultType="java.lang.String">
    select act_id
    from rv_authprj_cert_obtain_item
    where org_id = #{orgId}
      and authprj_cert_id = #{authPrjCertId}
      and deleted = 0
  </select>
</mapper>