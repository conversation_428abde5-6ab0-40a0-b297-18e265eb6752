<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpDeptMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptPO">
        <!--@mbg.generated-->
        <!--@Table udp_dept-->
        <result column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="parent_id" jdbcType="CHAR" property="parentId"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="CODE" jdbcType="VARCHAR" property="code"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="id_full_path" jdbcType="VARCHAR" property="idFullPath"/>
        <result column="routing_path" jdbcType="VARCHAR" property="routingPath"/>
        <result column="third_id" jdbcType="VARCHAR" property="thirdId"/>
        <result column="order_index" jdbcType="INTEGER" property="orderIndex"/>
        <result column="has_virtual_dept" jdbcType="TINYINT" property="hasVirtualDept"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="third_sync_time" jdbcType="TIMESTAMP" property="thirdSyncTime"/>
        <result column="mgt_sync_time" jdbcType="TIMESTAMP" property="mgtSyncTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , parent_id
             , name
             , code
             , description
             , id_full_path
             , routing_path
             , third_id
             , order_index
             , has_virtual_dept
             , create_user_id
             , create_time
             , update_user_id
             , update_time
             , deleted
             , third_sync_time
             , mgt_sync_time
    </sql>

    <select id="selectThirdDeptId" resultType="java.lang.String">
        select distinct third_id
        from udp_dept a
        where a.org_id = #{orgId}
          and a.third_id != ''
        <choose>
            <when test="deptIds != null and deptIds.size() != 0">
                and id in
                <foreach collection="deptIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectRootDeptId" resultType="java.lang.String">
        select id from udp_dept a where a.org_id = #{orgId} and a.parent_id = '' and a.deleted = 0 limit 1
    </select>

    <select id="selectByOrgIdAndId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from udp_dept a
        where a.org_id = #{orgId}
          and a.id = #{deptId}
          and a.deleted = 0
    </select>

    <select id="selectByOrgIdAndIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from udp_dept a
        where a.org_id = #{orgId}
          and a.deleted = 0
        <choose>
            <when test="deptIds != null and deptIds.size() != 0">
                and a.id in
                <foreach collection="deptIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByOrgIdAndThirdIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from udp_dept a
        where a.org_id = #{orgId}
          and a.deleted = 0
        <choose>
            <when test="thirdIds != null and thirdIds.size() != 0">
                and a.third_id in
                <foreach collection="thirdIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByOrgIdAndThirdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from udp_dept a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.third_id = #{thirdId}
    </select>

    <select id="selectByRoutingPath"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptPO">
        select
        <include refid="Base_Column_List"/>
        from udp_dept a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.routing_path like concat(#{routingPath}, '%')
    </select>

    <select id="selectByParentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from udp_dept a
        where a.org_id = #{orgId}
          and a.parent_id = #{parentId}
          and a.deleted = 0
    </select>

    <select id="selectChildDeptIds" resultType="java.lang.String">
        select distinct b.dept_id
        from udp_dept          a
        join udp_dept_relation b on a.id = b.parent_id and a.org_id = b.org_id and b.deleted = 0
        where a.org_id = #{orgId}
          and a.deleted = 0
        <choose>
          <when test="parentDeptIds != null and parentDeptIds.size() != 0">
            and a.id in
            <foreach collection="parentDeptIds" item="item" index="index" open="(" separator="," close=")">
              #{item}
            </foreach>
          </when>
          <otherwise>
            <!--@ignoreSql-->
            and 1 != 1
          </otherwise>
        </choose>
    </select>

    <select id="selectChildrenByDeptIds" resultMap="BaseResultMap">
      select c.id
           , c.order_index
           , c.org_id
           , c.parent_id
           , c.name
           , c.code
           , c.description
           , c.id_full_path
           , c.routing_path
           , c.third_id
      from udp_dept          a
      join udp_dept_relation b on a.id = b.parent_id and a.org_id = b.org_id and b.deleted = 0
      join udp_dept      c on b.dept_id = c.id and c.deleted = 0
      where a.org_id = #{orgId}
        and a.deleted = 0
      <choose>
        <when test="parentDeptIds != null and parentDeptIds.size() != 0">
          and a.id in
          <foreach collection="parentDeptIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
        </when>
        <otherwise>
          <!--@ignoreSql-->
          and 1 != 1
        </otherwise>
      </choose>
    </select>

    <select id="selectByOrgIdAndNames" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from udp_dept a
      where a.org_id = #{orgId}
      and a.deleted = 0
      and a.name in
      <foreach collection="deptNames" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </select>

    <select id="selectExtByOrgIdAndNames"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptExtPO">
      select id, org_id,routing_path_name from udp_dept_ext where org_id=#{orgId} and routing_path_name in
      <foreach collection="deptNames" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
      and (deleted =0 or deleted is null)
    </select>
</mapper>
