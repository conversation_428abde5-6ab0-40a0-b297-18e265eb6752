<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjOverviewMapper">

    <!-- 统计参与人数 -->
    <select id="countParticipants" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT apm.user_id)
        FROM rv_authprj ap
        INNER JOIN rv_activity_participation_member apm 
            ON ap.org_id = apm.org_id 
            AND ap.aom_prj_id = apm.actv_id 
            AND apm.deleted = 0
        WHERE ap.org_id = #{orgId}
          AND ap.id = #{authprjId}
          AND ap.deleted = 0
    </select>

  <!-- 统计完成人数 -->
  <select id="countCompleted" resultType="java.lang.Integer">
    SELECT count(DISTINCT rams.user_id)
    FROM rv_activity_member_statistics rams
           INNER JOIN rv_authprj ap ON rams.org_id = ap.org_id AND rams.actv_id = ap.aom_prj_id
    WHERE rams.org_id = #{orgId}
      AND ap.id = #{authprjId}
      AND rams.deleted = 0
      AND ap.deleted = 0
      AND rams.actv_completed_rate >= 1
  </select>

    <!-- 统计通过人数 -->
    <select id="countPassed" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT aru.user_id)
        FROM rv_authprj_result_user aru
        INNER JOIN rv_authprj ap ON aru.org_id = ap.org_id AND aru.authprj_id = ap.id
        INNER JOIN rv_authprj_rule_level arl 
            ON aru.org_id = arl.org_id 
            AND aru.authprj_id = arl.authprj_id 
            AND aru.level_id = arl.id
            AND arl.deleted = 0
        WHERE aru.org_id = #{orgId}
          AND aru.authprj_id = #{authprjId}
          AND aru.deleted = 0
          AND ap.deleted = 0
          AND arl.passed = 1
    </select>

    <!-- 获取指标统计数据 -->
    <select id="getIndicatorStatistics" resultType="com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjIndicatorStatisticsVO">
        SELECT
            arui.sd_indicator_id as firstIndicatorId,
            MAX(arui.score_total) as scoreTotal,
            AVG(arui.score) as score
        FROM rv_authprj_result_user_indicator arui
        INNER JOIN rv_authprj ap ON arui.org_id = ap.org_id AND arui.authprj_id = ap.id
        WHERE arui.org_id = #{orgId}
          AND arui.authprj_id = #{authprjId}
          AND arui.deleted = 0
          AND ap.deleted = 0
          <if test="indicatorIds != null and indicatorIds.size() > 0">
            AND arui.sd_indicator_id IN
            <foreach collection="indicatorIds" item="indicatorId" open="(" separator="," close=")">
                #{indicatorId}
            </foreach>
          </if>
        GROUP BY arui.sd_indicator_id
        ORDER BY arui.sd_indicator_id
    </select>

</mapper>
