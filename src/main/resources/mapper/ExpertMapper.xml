<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.ExpertMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.ExpertPO">
    <!--@mbg.generated-->
    <!--@Table rv_expert-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="user_id" property="userId" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="remark" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, user_id, deleted, create_time, create_user_id, update_time, update_user_id,remark
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_expert
    (id, org_id, user_id, deleted, create_time, create_user_id, update_time, update_user_id,remark
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.userId}, #{item.deleted}, #{item.createTime}, 
        #{item.createUserId}, #{item.updateTime}, #{item.updateUserId},#{item.remark})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    user_id=values(user_id),
    deleted=values(deleted),
    create_time=values(create_time),
    create_user_id=values(create_user_id),
    update_time=values(update_time),
    update_user_id=values(update_user_id),
    remark=values(remark)
  </insert>

  <select id="pageList" resultMap="BaseResultMap">
    select a.id, a.org_id, a.user_id,b.username,b.fullname,b.dept_name as deptName,b.position_name as positionName from rv_expert a join udp_lite_user_sp b on a.user_id=b.id where a.deleted=0 and a.org_id=#{criteria.orgId}
    <if test="criteria != null and criteria.keyword != '' and criteria.keyword != null">
        <if test="criteria.keywordType == 1">
           and b.fullname like concat('%', #{criteria.keyword}, '%') escape '\\'
        </if>

        <if test="criteria.keywordType == 2">
           and b.username like concat('%', #{criteria.keyword}, '%') escape '\\'
        </if>
    </if>

    <if test="criteria != null and criteria.deptIds != null and criteria.deptIds.size() > 0">
        and exists(select 1 from rv_expert_dept c where c.expert_id = a.id and c.deleted=0 and c.dept_id in
        <foreach collection="criteria.deptIds" item="itemId" open="(" close=")" separator=",">
          #{itemId}
        </foreach>
      )
    </if>

    <if test="criteria != null and criteria.indicatorIds != null and criteria.indicatorIds.size() > 0">
      and exists(select 1 from rv_expert_indicator d where d.expert_id = a.id and d.deleted=0 and d.ind_id in
      <foreach collection="criteria.indicatorIds" item="itemId" open="(" close=")" separator=",">
        #{itemId}
      </foreach>
      )
    </if>
    order by a.update_time desc,a.id
  </select>

  <select id="findByOrgIdAndUserId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from rv_expert where org_id=#{orgId} and user_id=#{userId} and deleted=0
  </select>

  <select id="findByOrgId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include>
    from rv_expert where org_id=#{orgId} and deleted=0
    </select>

  <select id="findCorrectExperts" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
        </include>
    from rv_expert a where a.org_id=#{orgId} and a.deleted=0
    and exists (
    select  1 from rv_expert_dept b where a.id=b.expert_id and b.deleted=0 and b.dept_id=#{deptId}
    )
    and exists (
    select  1 from rv_expert_indicator b where a.id=b.expert_id and b.deleted=0 and b.ind_id=#{indicatorId}
    )
    </select>
</mapper>