<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO">
    <!--@mbg.generated-->
    <!--@Table rv_authprj-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="aom_prj_id" property="aomPrjId" />
    <result column="model_id" property="modelId" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, aom_prj_id, model_id, deleted, create_time, create_user_id, update_time, 
    update_user_id
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_authprj
    (id, org_id, aom_prj_id, model_id, deleted, create_time, create_user_id, update_time, 
      update_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.aomPrjId}, #{item.modelId}, #{item.deleted}, #{item.createTime}, 
        #{item.createUserId}, #{item.updateTime}, #{item.updateUserId})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    aom_prj_id=values(aom_prj_id),
    model_id=values(model_id),
    deleted=values(deleted),
    create_time=values(create_time),
    create_user_id=values(create_user_id),
    update_time=values(update_time),
    update_user_id=values(update_user_id)
  </insert>

  <select id="selectByIdAndOrg" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from rv_authprj
    where org_id = #{orgId}
      and id = #{authprjId}
      and deleted = 0
  </select>

  <select id="selectByAomPrjId" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from rv_authprj
    where aom_prj_id = #{aomPrjId}
    and org_id = #{orgId}
    and deleted = 0
  </select>

  <select id="selectByOrgIdAndId" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO">
    select <include refid="Base_Column_List" />,
    (select actv_name from rv_activity b where b.org_id = a.org_id and b.id = a.aom_prj_id) as aomPrjName,
    (select actv_status from rv_activity b where b.org_id = a.org_id and b.id = a.aom_prj_id) as actvStatus
    from rv_authprj a
    where a.org_id = #{orgId}
      and a.id = #{id}
  </select>

  <select id="selectByAomIds" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from rv_authprj
    where org_id = #{orgId}
    and deleted = 0
    <if test="(aomPrjIds != null and aomPrjIds.size()>0)">
      and aom_prj_id in
        <foreach collection="aomPrjIds" item="aomId" open="(" close=")" separator=",">
            #{aomId}
        </foreach>
    </if>
  </select>

  <update id="deleteByAomPrjIds">
    <!--@mbg.generated-->
    update rv_authprj set deleted = 1
    where org_id = #{orgId} and deleted = 0
    <if test="(aomPrjIds != null and aomPrjIds.size() > 0)">
      and aom_prj_id in
      <foreach collection="aomPrjIds" item="aomPrjId" open="(" close=")" separator=",">
        #{aomPrjId}
      </foreach>
    </if>
  </update>
</mapper>