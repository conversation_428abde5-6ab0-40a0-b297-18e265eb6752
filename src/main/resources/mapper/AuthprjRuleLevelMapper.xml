<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjRuleLevelMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjRuleLevelPO">
    <!--@mbg.generated-->
    <!--@Table rv_authprj_rule_level-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="authprj_id" property="authprjId" />
    <result column="level_name" property="levelName" />
    <result column="order_index" property="orderIndex" />
    <result column="passed" property="passed" />
    <result column="formula" property="formula" />
    <result column="formula_display" property="formulaDisplay" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, authprj_id, level_name, order_index, passed, formula, formula_display, 
    deleted, create_user_id, create_time, update_user_id, update_time
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_authprj_rule_level
    (id, org_id, authprj_id, level_name, order_index, passed, formula, formula_display, 
      deleted, create_user_id, create_time, update_user_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.authprjId}, #{item.levelName}, #{item.orderIndex}, 
        #{item.passed}, #{item.formula}, #{item.formulaDisplay}, #{item.deleted}, #{item.createUserId}, 
        #{item.createTime}, #{item.updateUserId}, #{item.updateTime})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    authprj_id=values(authprj_id),
    level_name=values(level_name),
    order_index=values(order_index),
    passed=values(passed),
    formula=values(formula),
    formula_display=values(formula_display),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time)
  </insert>

  <!-- 根据认证项目ID查询分层规则列表 -->
  <select id="selectByAuthprjId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_authprj_rule_level
    where org_id = #{orgId,jdbcType=CHAR}
      and authprj_id = #{authprjId,jdbcType=CHAR}
      and deleted = 0
    order by order_index, create_time
  </select>

  <!-- 根据认证项目ID和分层名称查询分层规则 -->
  <select id="selectByAuthprjIdAndLevelName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from rv_authprj_rule_level
    where org_id = #{orgId,jdbcType=CHAR}
      and authprj_id = #{authprjId,jdbcType=CHAR}
      and level_name = #{levelName,jdbcType=VARCHAR}
      and deleted = 0
    limit 1
  </select>

  <update id="deleteByAuthprjId">
    update rv_authprj_rule_level set deleted = 1, update_time = now(), update_user_id = #{operator}
    where org_id = #{orgId} and deleted = 0 and authprj_id = #{authprjId}
  </update>

</mapper>