<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityArrangeItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvActivityArrangeItemPO">
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="actv_id" property="actvId"/>
        <result column="actv_org_id" property="actvOrgId"/>
        <result column="parent_id" property="parentId"/>
        <result column="ref_reg_id" property="refRegId"/>
        <result column="ref_id" property="refId"/>
        <result column="ref_name" property="refName"/>
        <result column="item_name" property="itemName"/>
        <result column="item_type" property="itemType"/>
        <result column="type_name_key" property="typeNameKey"/>
        <result column="actv_alias" property="actvAlias"/>
        <result column="node_level" property="nodeLevel"/>
        <result column="description" property="description"/>
        <result column="required" property="required"/>
        <result column="locked" property="locked"/>
        <result column="hidden" property="hidden"/>
        <result column="order_index" property="orderIndex"/>
        <result column="study_hours" property="studyHours"/>
        <result column="ext" property="ext"/>
        <result column="time_model" property="timeModel"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="start_day_offset" property="startDayOffset"/>
        <result column="end_day_offset" property="endDayOffset"/>
        <result column="item_status" property="itemStatus"/>
        <result column="deleted" property="deleted"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, org_id, actv_id, actv_org_id, parent_id, ref_reg_id, ref_id, ref_name, item_name, item_type, type_name_key, actv_alias, node_level, description, required, locked, hidden, order_index, study_hours, ext, time_model, start_time, end_time, start_day_offset, end_day_offset, item_status, deleted, create_user_id, update_user_id, create_time, update_time
    </sql>

    <select id="listRefRegId" resultType="java.lang.String">
        select ref_reg_id
        from rv_activity_arrange_item
        where org_id = #{orgId}
        <if test="actvId!= null">
            and actv_id = #{actvId}
        </if>
        <if test="actvOrgId!= null">
            and actv_org_id = #{actvOrgId}
        </if>
        and deleted = 0
        and item_type = 0
    </select>

    <select id="listArrangeItem" resultType="com.yxt.aom.base.entity.arrange.ActivityArrangeItem">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_arrange_item
        where org_id = #{orgId}
        <if test="actvId!= null">
            and actv_id = #{actvId}
        </if>
        <if test="actvOrgId!= null">
            and actv_org_id = #{actvOrgId}
        </if>
        and deleted = 0
        <if test="itemType!= null">
            and item_type = #{itemType}
        </if>
        order by parent_id, order_index, create_time
    </select>

    <select id="itemByRefIdAndActvId" resultType="com.yxt.aom.base.entity.arrange.ActivityArrangeItem">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_arrange_item
        where org_id=#{orgId}
        and actv_id=#{actvId}
        and ref_id=#{refId}
        and deleted=0
    </select>

    <update id="deleteByIds">
        update aom_activity_arrange_item set deleted = 1, update_user_id = #{operatorId}, update_time =
        current_timestamp(3)
        where org_id = #{orgId}
        and id in
        <foreach collection="itemIds" item="itemId" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="com.yxt.aom.base.entity.arrange.ActivityArrangeItem">
        insert into rv_activity_arrange_item
        (<include refid="Base_Column_List"/>)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id}, #{item.orgId}, #{item.actvId}, #{item.actvOrgId}, #{item.parentId}, #{item.refRegId},
            #{item.refId}, #{item.refName}, #{item.itemName}, #{item.itemType}, #{item.typeNameKey}, #{item.actvAlias},
            #{item.nodeLevel}, #{item.description}, #{item.required}, #{item.locked}, #{item.hidden},
            #{item.orderIndex}, #{item.studyHours}, #{item.ext}, #{item.timeModel}, #{item.startTime},
            #{item.endTime}, #{item.startDayOffset}, #{item.endDayOffset}, #{item.itemStatus}, #{item.deleted},
            #{item.createUserId}, #{item.updateUserId}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update rv_activity_arrange_item
            <set>
                org_id = #{item.orgId}, actv_id = #{item.actvId}, actv_org_id = #{item.actvOrgId},
                parent_id = #{item.parentId}, ref_reg_id = #{item.refRegId}, ref_id = #{item.refId},
                ref_name = #{item.refName}, item_name = #{item.itemName}, item_type = #{item.itemType},
                type_name_key = #{item.typeNameKey}, actv_alias = #{item.actvAlias}, node_level = #{item.nodeLevel},
                description = #{item.description}, required = #{item.required}, locked = #{item.locked},
                hidden = #{item.hidden}, order_index = #{item.orderIndex}, study_hours = #{item.studyHours},
                ext = #{item.ext}, time_model = #{item.timeModel},
                start_time = #{item.startTime}, end_time = #{item.endTime}, start_day_offset = #{item.startDayOffset},
                end_day_offset = #{item.endDayOffset}, item_status = #{item.itemStatus}, deleted = #{item.deleted},
                create_user_id = #{item.createUserId}, update_user_id = #{item.updateUserId},
                create_time = #{item.createTime}, update_time = #{item.updateTime}
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <update id="updateStudyHours">
        update rv_activity_arrange_item set study_hours = #{studyHours}
        where org_id = #{orgId}
        and id in
        <foreach collection="itemIds" item="itemId" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
    </update>

    <select id="getItemByParentId" resultType="com.yxt.aom.base.entity.arrange.ActivityArrangeItem">
        select
            <include refid="Base_Column_List"/>
        from rv_activity_arrange_item
        where org_id = #{orgId}
        and actv_id = #{actvId}
        and parent_id in
        <foreach collection="parentIds" item="itemId" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
        and deleted=0
    </select>

  <select id="listInfoByIds" resultType="com.yxt.talent.rv.application.xpd.common.dto.ActivityInfo4UserDto">
      select a.ref_id refId, a.actv_id actvId,a.ref_name actvName
      # b.actv_name actvName
      from rv_activity_arrange_item a
      # left join rv_activity b
      # on a.org_id = b.org_id
      # and a.ref_id = b.id
      # and b.deleted = 0
      where a.org_id = #{orgId}
      and a.ref_id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
          #{id}
      </foreach>
      and a.deleted = 0
  </select>

  <select id="listArrangeItemForSearch" resultType="com.yxt.aom.base.entity.arrange.ActivityArrangeItem">
    select
    <include refid="Base_Column_List"/>
    from rv_activity_arrange_item
    where org_id = #{orgId}
    <if test="searchDto.actvId!= null">
      and actv_id = #{searchDto.actvId}
    </if>
    <if test="searchDto.actvOrgId!= null">
      and actv_org_id = #{searchDto.actvOrgId}
    </if>
    and deleted = 0 and hidden = 0
    <if test="searchDto.itemType!= null">
      and item_type = #{searchDto.itemType}
    </if>
    <if test="searchDto.keyword!= null and searchDto.keyword != ''">
      and ref_name like concat('%', #{searchDto.keyword}, '%')
    </if>
    <if test="searchDto.refRegIds != null and searchDto.refRegIds.size() != 0">
      and ref_reg_id in
      <foreach collection="searchDto.refRegIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    order by node_level, order_index, create_time
  </select>

    <select id="countByAuthprjId" resultType="int">
        select count(1)
        from rv_activity_arrange_item a
        join rv_authprj b on a.org_id = b.org_id and a.actv_id = b.aom_prj_id and b.deleted = 0
        where a.org_id = #{orgId} and a.deleted = 0 and b.id = #{authprjId}
    </select>
</mapper>
