package com.yxt.talent.rv.infrastructure.trigger.message.rocket.cert;

import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.application.authprj.AuthPrjCertIssueService;
import com.yxt.talent.rv.application.authprj.AuthPrjService;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.AUTHPRJ_NOT_EXIST;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CER_ISSUE_STATUS_CHANGE_TOPIC;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;

/**
 * 证书状态变更事件消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    consumerGroup = CONSUMER_GROUP_PREFIX + CER_ISSUE_STATUS_CHANGE_TOPIC,
    topic = CER_ISSUE_STATUS_CHANGE_TOPIC,
    consumeThreadNumber = 2,
    consumeTimeout = 30
)
public class CertIssueChangeConsumer implements RocketMQListener<CertIssueChangeEvent> {

    private final AuthPrjCertIssueService authPrjCertIssueService;
    private final AuthPrjService authPrjService;
    private final AuthprjMapper authprjMapper;

    @Override
    public void onMessage(CertIssueChangeEvent message) {
        try {
            log.info("LOG10156:收到证书状态变更事件: {}", message);

            if (!Objects.equals(message.getSourceType(), AppConstants.AUTHPRJ_CERT_SOURCE_FLAG)) {
                return;
            }

            String aomPrjId = message.getSourceId();
            AuthprjPO authprjPO = authprjMapper.selectByAomPrjId(message.getOrgId(), aomPrjId);
            Validate.isNotNull(authprjPO, AUTHPRJ_NOT_EXIST);
            String authPrjId = authprjPO.getId();

            // 根据事件类型更新本地证书状态
            Integer certStatus = mapEventTypeToCertStatus(message.getType());
            if (certStatus != null) {
                authPrjCertIssueService.updateCertStatus(
                    message.getOrgId(),
                    authPrjId,
                    message.getCerId(),
                    Collections.singletonList(message.getUserId()),
                    certStatus
                );
                log.info("LOG10166:更新证书状态成功: message={}, status={}", BeanHelper.bean2Json(message, ALWAYS), certStatus);

                // 如果证书状态为已过期，自动指派员工重新认证
                if (certStatus == 4) {
                    log.info("LOG40026:证书已过期，自动指派员工重新认证: message={}", BeanHelper.bean2Json(message, ALWAYS));
                    try {
                        authPrjService.reStartPrj(
                            message.getOrgId(), "cert_issue_expired", authPrjId,
                            Collections.singletonList(message.getUserId()));
                    } catch(Exception e) {
                        log.warn("LOG40036:自动指派员工重新认证失败:", e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("LOG10176:处理证书状态变更事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 将事件类型映射为本地证书状态
     */
    private Integer mapEventTypeToCertStatus(Integer eventType) {
        if (eventType == null) {
            return null;
        }

        return switch (eventType) {
            case 0 -> 1;  // 颁发成功 -> 已发布
            case 1 -> 2;  // 已吊销 -> 被撤销
            case 2 -> 3;  // 已删除 -> 已删除
            case 3 -> 4;  // 已过期 -> 已过期
            default -> null;
        };
    }
}
