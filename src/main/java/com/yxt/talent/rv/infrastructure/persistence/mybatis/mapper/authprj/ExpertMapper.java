package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.talent.rv.application.expert.ExpertPageVO;
import com.yxt.talent.rv.application.expert.ExpertSearchCriteria;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.ExpertPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExpertMapper extends CommonMapper<ExpertPO>, BaseMapper<ExpertPO> {
    int batchInsertOrUpdate(@Param("list") List<ExpertPO> list);

    IPage<ExpertPO> pageList(Page<ExpertPO> page, @Param("criteria") ExpertSearchCriteria searchParam);

    ExpertPO findByOrgIdAndUserId(@Param("orgId") String orgId, @Param("userId") String userId);

    List<ExpertPO> findByOrgId(@Param("orgId") String orgId);

    List<ExpertPO> findCorrectExperts(@Param("orgId") String orgId, @Param("deptId") String deptId, @Param("indicatorId") String indicatorId);
}