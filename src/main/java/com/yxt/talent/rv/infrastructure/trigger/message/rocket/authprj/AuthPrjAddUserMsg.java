package com.yxt.talent.rv.infrastructure.trigger.message.rocket.authprj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 添加用户
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthPrjAddUserMsg implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 活动ID
     */
    private String authPrjId;

    @Schema(description = "userIds")
    private Set<String> userIds;

    @Schema(description = "加入方式（7-岗位地图 8-层级地图）")
    private Integer joinMethod;
    /**
     * 加入时间
     */
    private Date startTime;
}
