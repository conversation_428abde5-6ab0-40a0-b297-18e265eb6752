package com.yxt.talent.rv.infrastructure.trigger.message.rocket.activity;

import com.yxt.aom.base.bean.control.MemberStatisticsChangeMsg;
import com.yxt.event.EventPublisher;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_MEMBER_STATISTICS_CHANGE;

@Service
@RequiredArgsConstructor
@RocketMQMessageListener(consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_MEMBER_STATISTICS_CHANGE,
    topic = TOPIC_MEMBER_STATISTICS_CHANGE, consumeThreadNumber = 1, consumeTimeout = 30)
public class AomMemberStatisticChangeConsumer implements RocketMQListener<MemberStatisticsChangeMsg> {

    private static final Logger log = LoggerFactory.getLogger(AomMemberStatisticChangeConsumer.class);
    private final EventPublisher eventPublisher;

    @Override
    public void onMessage(MemberStatisticsChangeMsg message) {
        try {
            eventPublisher.publish(new AomMemberStatisticChangeEvent(message));
        } catch (Exception e) {
            log.warn("LOG42286:", e);
        }
    }
}
