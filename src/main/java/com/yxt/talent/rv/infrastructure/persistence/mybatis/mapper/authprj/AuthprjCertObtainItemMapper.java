package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjCertObtainItemPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthprjCertObtainItemMapper extends CommonMapper<AuthprjCertObtainItemPO>, BaseMapper<AuthprjCertObtainItemPO> {
    int batchInsertOrUpdate(@Param("list") List<AuthprjCertObtainItemPO> list);

    /**
     * 根据证书ID删除获取条件配置
     */
    int deleteByAuthprjCertId(
        @Param("orgId") String orgId, @Param("authprjCertId") String authprjCertId,
        @Param("operator") String operator);

    List<String> selectObtainItemsByCertId(@Param("orgId") String orgId, @Param("authPrjCertId") String authPrjCertId);
}