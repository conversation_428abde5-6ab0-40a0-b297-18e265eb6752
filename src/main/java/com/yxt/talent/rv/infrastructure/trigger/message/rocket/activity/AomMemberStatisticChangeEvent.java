package com.yxt.talent.rv.infrastructure.trigger.message.rocket.activity;

import com.yxt.aom.base.bean.control.MemberStatisticsChangeMsg;
import com.yxt.event.message.MessageEvent;
import lombok.*;

/**
 * aom学员活动进度发生变化时的回调通知
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class AomMemberStatisticChangeEvent extends MessageEvent {

    private MemberStatisticsChangeMsg message;

}
