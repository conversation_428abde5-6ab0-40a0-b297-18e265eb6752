package com.yxt.talent.rv.infrastructure.trigger.message.rocket.authprj;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.base.bean.part.ActivityMember4Add;
import com.yxt.aom.base.component.part.ParticipationComponent;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeForAuthPrjEnum;
import com.yxt.talent.rv.infrastructure.common.constant.MQConstant;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;

/**
 * 认证项目用户结果处理消费者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(consumerGroup = CONSUMER_GROUP_PREFIX + MQConstant.TOPIC_AUTH_PRJ_ADD_USER,
    topic = MQConstant.TOPIC_AUTH_PRJ_ADD_USER,
    consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadNumber = 3)
public class AuthPrjAddUserConsumer implements RocketMQListener<AuthPrjAddUserMsg> {

    private final ParticipationComponent participationComponent;
    private final AuthprjMapper authprjMapper;
    private final ActivityParticipationService activityParticipationService;

    @Override
    public void onMessage(AuthPrjAddUserMsg message) {
        String userId = message.getUserId();
        String orgId = message.getOrgId();
        String authPrjId = message.getAuthPrjId();
        log.info("AuthPrjAddUserConsumer, message={}", BeanHelper.bean2Json(message, JsonInclude.Include.NON_NULL));
        try {
            AuthprjPO authprjPO = authprjMapper.selectByIdAndOrg(orgId, authPrjId);
            if (authprjPO == null){
                log.info("未找到认证项目");
                return;
            }
            ActivityMember4Add activityMember4Add = new ActivityMember4Add();
            activityMember4Add.setActvId(authprjPO.getAomPrjId());

            Long partId = activityParticipationService.getParticipationId(orgId, authprjPO.getAomPrjId());
            if (Objects.isNull(partId)) {
                log.info("未找到认证项目partId");
                return;
            }

            activityMember4Add.setParticipationId(partId);
            activityMember4Add.setFormal(1);
            activityMember4Add.setStartTimeForCycle(message.getStartTime());
            activityMember4Add.setRegId(UacdTypeForAuthPrjEnum.PRJ_AUTH.getRegId());
            activityMember4Add.setJoinMethod(message.getJoinMethod());
            activityMember4Add.setUserIds(new ArrayList<>(message.getUserIds()));

            participationComponent.addMember(activityMember4Add, orgId, userId);
            log.info("LOG20046:用户认证结果处理完成, userId={}, orgId={}, authPrjId={}", userId, orgId, authPrjId);

        } catch (Exception e) {
            log.error("AuthPrjAddUserConsumer,  error={}",e.getMessage(), e);
        }
    }
}
