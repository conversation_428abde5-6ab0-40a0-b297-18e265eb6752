package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjRuleLevelPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AuthprjRuleLevelMapper extends CommonMapper<AuthprjRuleLevelPO>, BaseMapper<AuthprjRuleLevelPO> {

    /**
     * 批量插入或更新
     */
    int batchInsertOrUpdate(@Param("list") List<AuthprjRuleLevelPO> list);

    /**
     * 根据认证项目ID查询分层规则列表
     */
    List<AuthprjRuleLevelPO> selectByAuthprjId(@Param("orgId") String orgId, @Param("authprjId") String authprjId);

    /**
     * 根据认证项目ID和分层名称查询分层规则
     */
    AuthprjRuleLevelPO selectByAuthprjIdAndLevelName(
        @Param("orgId") String orgId, @Param("authprjId") String authprjId,
        @Param("levelName") String levelName);

    @Select("""
        select count(1) from rv_authprj_rule_level where org_id = #{orgId} and authprj_id = #{authprjId} and deleted = 0
    """)
    int countByAuthprjId(@Param("orgId") String orgId, @Param("authprjId") String authprjId);

    void deleteByAuthprjId(
        @Param("orgId") String orgId, @Param("authprjId") String authprjId, @Param("operator") String operator);
}