package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AuthprjMapper extends CommonMapper<AuthprjPO>, BaseMapper<AuthprjPO> {
    int batchInsertOrUpdate(@Param("list") List<AuthprjPO> list);

    /**
     * 根据机构ID和认证项目ID查询认证项目
     */
    AuthprjPO selectByIdAndOrg(@Param("orgId") String orgId, @Param("authprjId") String authprjId);

    AuthprjPO selectByAomPrjId(@Param("orgId") String orgId, @Param("aomPrjId") String aomPrjId);

    @Select("""
    select 1 from rv_activity where org_id = #{orgId} and deleted = 0
    and category_id in (
            select n.descnt_id from rv_tree_node_relation n where n.org_id=#{orgId} and n.tree_id=#{treeId} and
            n.ancr_id = #{catalogId} and n.deleted = 0
            ) limit 1
    """)
    Integer existActvByCategoryId(
        @Param("orgId") String orgId,
        @Param("treeId") String treeId,
        @Param("catalogId") String catalogId);

    AuthprjPO selectByOrgIdAndId(@Param("orgId") String orgId, @Param("id") String id);

    List<AuthprjPO> selectByAomIds(@Param("orgId") String orgId,@Param("aomPrjIds") List<String> aomIds);

    void deleteByAomPrjIds(@Param("orgId") String orgId, @Param("aomPrjIds") List<String> aomPrjIds);
}