package com.yxt.talent.rv.infrastructure.trigger.message.rocket.authprj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 删除项目
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthPrjDelMsg implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 活动ID
     */
    private String authPrjId;

}
