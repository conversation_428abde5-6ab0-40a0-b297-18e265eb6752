package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptExtPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface UdpDeptMapper {

    @Nonnull
    List<String> selectThirdDeptId(
            @Nonnull @Param("orgId") String orgId, @Param("deptIds") List<String> deptIds);

    @Nullable
    String selectRootDeptId(@Nonnull @Param("orgId") String orgId);

    @Nullable
    UdpDeptPO selectByOrgIdAndId(
            @Nonnull @Param("orgId") String orgId, @Param("deptId") String deptId);

    @Nonnull
    List<UdpDeptPO> selectByOrgIdAndIds(
            @Nonnull @Param("orgId") String orgId, @Param("deptIds") List<String> deptIds);

    @Nonnull
    List<UdpDeptPO> selectByOrgIdAndThirdIds(
            @Nonnull @Param("orgId") String orgId, @Param("thirdIds") List<String> thirdIds);


    @Nullable
    UdpDeptPO selectByOrgIdAndThirdId(
            @Nonnull @Param("orgId") String orgId, @Param("thirdId") String thirdId);

    @Nonnull
    Collection<UdpDeptPO> selectByRoutingPath(
            @Nonnull @Param("orgId") String orgId, @Param("routingPath") String routingPath);

    @Nonnull
    Collection<UdpDeptPO> selectByParentId(
            @Nonnull @Param("orgId") String orgId, @Param("parentId") String parentId);

    List<String> selectChildDeptIds(
        @Param("orgId") String orgId, @Param("parentDeptIds") Collection<String> parentDeptIds);

    List<UdpDeptPO> selectChildrenByDeptIds(
        @Param("orgId") String orgId, @Param("parentDeptIds") Collection<String> parentDeptIds);

    List<UdpDeptPO> selectByOrgIdAndNames(@Param("orgId") String orgId, @Param("deptNames") List<String> deptNameList);

    List<UdpDeptExtPO> selectExtByOrgIdAndNames(@Param("orgId") String orgId, @Param("deptNames") List<String> deptNameList);
}
