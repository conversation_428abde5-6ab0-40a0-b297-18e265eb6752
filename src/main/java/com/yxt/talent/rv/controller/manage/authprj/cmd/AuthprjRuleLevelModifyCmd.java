package com.yxt.talent.rv.controller.manage.authprj.cmd;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 认证项目分层规则创建命令
 */
@Data
@Schema(description = "认证项目分层规则创建命令")
public class AuthprjRuleLevelModifyCmd {

    @JsonProperty("id")
    @Schema(description = "分层id，如果没有删除，则该id必传，新增的分层不需要传")
    private String levelId;

    @Schema(description = "分层名称")
    @NotBlank(message = ExceptionKeys.AUTHPRJ_RULE_LEVEL_NAME_REQUIRED)
    @Size(max = 200, message = ExceptionKeys.AUTHPRJ_RULE_LEVEL_NAME_SIZE)
    private String levelName;

    @Schema(description = "是否通过 0-未通过 1-通过")
    @NotNull(message = ExceptionKeys.AUTHPRJ_RULE_LEVEL_PASSED_REQUIRED)
    private Integer passed;

    @Schema(description = "计算规则JSON")
    @NotBlank(message = ExceptionKeys.AUTHPRJ_RULE_LEVEL_FORMULA_REQUIRED)
    private String formula;

}
