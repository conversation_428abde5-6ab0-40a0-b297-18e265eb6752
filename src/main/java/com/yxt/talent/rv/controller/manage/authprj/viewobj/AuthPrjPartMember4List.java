package com.yxt.talent.rv.controller.manage.authprj.viewobj;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.common.annotation.timezone.DateFormatField;
import com.yxt.common.enums.YesOrNo;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

import static com.yxt.common.Constants.INT_0;

/**
 * <AUTHOR>
 * @since 2025/07/14
 */
@Getter
@Setter
public class AuthPrjPartMember4List implements L10NContent {

    @Schema(description = "主键", example = "1210805511352545282")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "用户id", example = "1210805511352545282")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String userId;

    @Schema(description = "工号", example = "sok")
    private String userNo;

    @Schema(description = "账号", example = "sok")
    private String username;

    @Schema(description = "姓名", example = "张三")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String fullname;

    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.KEY)
    private String deptId;

    @Schema(description = "部门", example = "测试部门")
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    private String deptName;

    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.KEY)
    private String positionId;

    @Schema(description = "岗位", example = "测试岗位")
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String positionList;

    @Schema(description = "头像")
    private String avatarUrl;

    @Schema(description = "是否是讲师；0：否；1：是；")
    private Integer wasTeacher = YesOrNo.NO.getValue();

    @Schema(description = "加入方式（0：Unknown（默认）；1：管理员手动加入；2：自动加入；3：通过报名加入；5：通过动态用户组加入）")
    private Integer joinMethod;

    @Schema(description = "视图用户状态；0：已禁用；1：启用")
    private Integer status;

    @Schema(description = "视图用户删除状态；0：正常；1：已删除；")
    private Integer deleted;

    @Schema(description = "列表用户状态；0：正常；1：已禁用；2：已删除")
    private Integer userStatus;

    @DateFormatField(isDate = true)
    @Schema(description = "最近学习时间", example = "2021-08-08 17:11:58")
    private Date lastStudyTime;

    @DateFormatField(isDate = true)
    @Schema(description = "学员学习开始时间", example = "2021-08-08 17:11:58")
    private Date startTime;

    @DateFormatField(isDate = true)
    @Schema(description = "学员学习结束时间", example = "2021-08-08 17:11:58")
    private Date endTime;

    @DateFormatField(isDate = true)
    @Schema(description = "入职时间", example = "2021-08-08")
    private Date hireDate;

    @Schema(description = "加入时间", example = "2022-10-01 00:00:00")
    @DateFormatField(isDate = true)
    private Date joinTime;

    @Schema(description = "称号名称")
    private String title;

    @Schema(description = "称号id")
    private Long titleId;

    @Schema(description = "是否逾期 0-未逾期 1-逾期", example = "1")
    private Integer overdue = INT_0;

    @Schema(description = "所属平台名称")
    private String orgName;

    @Schema(description = "所属平台ID")
    private String orgId;

    @Schema(description = "1正式学员 0旁听学员", example = "1")
    private Integer formal;

    @Schema(description = "职级", example = "P1")
    private String gradeName;

    @Schema(description = "全部完成率")
    private BigDecimal allProcessRate;

    @Schema(description = "项目完成进度")
    private BigDecimal completeProcessRate;

    @Schema(description = "0-未开始 1-进行中 2-已完成")
    private int completeStatus;

    @Schema(description = "项目完成时间", example = "2022-10-09 00:00:00")
    @DateFormatField(isDate = true)
    private Date completeTime;


    @Schema(description = "整体进度，根据完成标准做呈现")
    private String fullProgress;

    private String allProgress;

     /**
     * 学员活动完成状态：0-未开始 1-进行中 2-已完成
     */
    private Integer actvCompletedStatus;

    @Schema(description = "活动完成率")
    private BigDecimal actCompletedRate;

    /**
     * 活动id
     */
    private String actvId;

    /**
     * 参与id
     */
    private Long partId;

    @Schema(description = "项目结果")
    private String prjResult;

    @Schema(description = "项目得分")
    private BigDecimal scoreValue;

}
