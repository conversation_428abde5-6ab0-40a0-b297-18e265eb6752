package com.yxt.talent.rv.controller.facade;

import com.yxt.common.annotation.Auth;
import com.yxt.talent.rv.application.expert.component.ExpertComponent;
import com.yxt.talentrvfacade.bean.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.AKSK;
import static com.yxt.common.enums.AuthType.CUSTOM;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("")
public class ExpertFacadeController {

    private final ExpertComponent expertComponent;

    @Auth(type = {AKSK, CUSTOM})
    @ResponseStatus(OK)
    @PostMapping(value = "/facade/expert/pick", produces = MEDIATYPE)
    public List<ExpertResp> findCorrectExperts(@Validated @RequestBody Exper4Facade req) {
        return expertComponent.findCorrectExperts(req);
    }

}
