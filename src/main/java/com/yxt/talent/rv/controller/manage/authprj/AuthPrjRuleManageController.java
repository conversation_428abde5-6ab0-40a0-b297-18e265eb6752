package com.yxt.talent.rv.controller.manage.authprj;

import com.yxt.common.annotation.Auth;
import com.yxt.spsdk.common.bean.RuleMainBase;
import com.yxt.spsdk.common.bean.SpRuleColumnMetaDto;
import com.yxt.spsdk.common.bean.UserBasicInfo;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.rv.application.authprj.AuthPrjCalcAppService;
import com.yxt.talent.rv.application.authprj.AuthPrjCalcTaskService;
import com.yxt.talent.rv.application.authprj.AuthPrjRuleAppService;
import com.yxt.talent.rv.controller.manage.authprj.cmd.AuthprjRuleLevelModifyCmd;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCalcStatusVO;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthprjRuleLevelVO;
import com.yxt.talent.rv.controller.manage.xpd.result.query.RuleColumnReq;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskProgress;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "认证项目-规则", description = "认证项目-规则")
@RequestMapping(value = "/mgr/authprj")
public class AuthPrjRuleManageController {

    private final SpRuleService spRuleService;
    private final AuthPrjRuleAppService authprjRuleAppService;
    private final AuthPrjCalcAppService authPrjCalcAppService;
    private final AuthPrjCalcTaskService authPrjCalcTaskService;

    @Nullable
    @Operation(summary = "认证项目分层规则配置数据")
    @PostMapping(value = "/config")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public List<SpRuleColumnMetaDto> ruleConfig(@RequestBody List<RuleColumnReq> columnList) {
        UserBasicInfo userCache = YxtBasicUtils.userInfo();
        List<SpRuleColumnMetaDto> retList = new ArrayList<>();

        for (RuleColumnReq ruleCol : columnList) {
            RuleMainBase mainData = new RuleMainBase();
            mainData.setOrgId(userCache.getOrgId());
            mainData.setLocale(userCache.getLocale());
            mainData.setBizId(ruleCol.getBizId()); // 认证项目ID

            SpRuleColumnMetaDto colMeta = spRuleService.ruleMetaInfo(mainData, ruleCol.getColumnType());
            if (colMeta != null) {
                retList.add(colMeta);
            }
        }

        return retList;
    }

    @Operation(summary = "查询认证项目分层规则列表")
    @Parameters({
        @Parameter(name = "authprjId", description = "认证项目id", in = ParameterIn.PATH)
    })
    @GetMapping(value = "/{authprjId}/rule/list")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public List<AuthprjRuleLevelVO> list(@PathVariable String authprjId) {
        UserBasicInfo userCache = YxtBasicUtils.userInfo();
        return authprjRuleAppService.list(userCache.getOrgId(), authprjId);
    }

    @Operation(summary = "创建&更新认证项目分层规则，至少保留2个分层规则")
    @PostMapping(value = "/{authprjId}/rule/modify")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public void modify(@Valid @RequestBody List<AuthprjRuleLevelModifyCmd> cmd, @PathVariable String authprjId) {
        UserBasicInfo userCache = YxtBasicUtils.userInfo();
        authprjRuleAppService.modify(userCache.getOrgId(), userCache.getUserId(), authprjId, cmd);
    }

    @Operation(summary = "重新计算员工的认证结果和指标得分")
    @Parameters({
        @Parameter(name = "authprjId", description = "认证项目id", in = ParameterIn.PATH)
    })
    @GetMapping(value = "/{authprjId}/rule/calculate")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public void calculate(@PathVariable("authprjId") String authprjId, @RequestParam(defaultValue = "false") Boolean isSync) {
        UserBasicInfo userCache = YxtBasicUtils.userInfo();
        authPrjCalcAppService.triggerProjectCalc(userCache.getOrgId(), authprjId, isSync);
    }

    @Operation(summary = "查询认证项目计算状态")
    @Parameters({
        @Parameter(name = "authprjId", description = "认证项目id", in = ParameterIn.PATH)
    })
    @GetMapping(value = "/{authprjId}/rule/calculate/status")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public AuthPrjCalcStatusVO getCalculateStatus(@PathVariable String authprjId) {
        UserBasicInfo userCache = YxtBasicUtils.userInfo();
        TaskProgress progress = authPrjCalcTaskService.getCalculationProgress(userCache.getOrgId(), authprjId);
        
        AuthPrjCalcStatusVO statusVO = new AuthPrjCalcStatusVO();
        if (progress != null) {
            statusVO.setCalculating(authPrjCalcTaskService.isCalculating(userCache.getOrgId(), authprjId));
            statusVO.setStateCode(progress.getStateCode());
            statusVO.setMessage(progress.getMessage());
            statusVO.setTimestamp(progress.getTimestamp());
        } else {
            statusVO.setCalculating(false);
            statusVO.setStateCode("NONE");
            statusVO.setMessage("未开始计算");
        }
        
        return statusVO;
    }
}
