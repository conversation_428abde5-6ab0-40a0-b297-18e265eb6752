package com.yxt.talent.rv.controller.client.authprj;


import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.common.annotation.Auth;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.activity.dto.UserProjectDTO;
import com.yxt.talent.rv.application.authprj.AuthPrjUserAppService;
import com.yxt.talent.rv.application.authprj.dto.AomUserStatisDataDTO;
import com.yxt.talent.rv.controller.client.authprj.query.PrjListQuery;
import com.yxt.talent.rv.controller.client.authprj.query.viewobj.PrjDetailVO;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.*;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthPrjUserOverviewMapper;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "认证项目-学员端", description = "认证项目-学员端")
@RequestMapping(value = "/client/authprj")
public class AuthPrjClientController {

    private final AuthPrjUserAppService authPrjUserAppService;
    private final AuthService authService;
    private final AuthPrjUserOverviewMapper authPrjUserOverviewMapper;
    private final ActivityService activityService;

    @Operation(summary = "项目列表接口")
    @PostMapping(value = "/project/list")
    @ResponseBody
    @Auth(codes = AUTH_CODE_ALL)
    public PagingList<UserProjectDTO> getUserPrjs(HttpServletRequest request, @RequestBody PrjListQuery prjListQuery) {
        UserCacheBasic userCache = authService.getUserCacheBasic();
        return authPrjUserAppService.getProjectList(userCache.getOrgId(), userCache.getUserId(), prjListQuery, ApiUtil.getPageRequest(request));
    }

    @Operation(summary = "学员项目基础信息")
    @GetMapping(value = "/project/detail/{id}")
    @ResponseBody
    @Auth(codes = AUTH_CODE_ALL)
    public PrjDetailVO getUserPrjs(HttpServletRequest request, @PathVariable String id) {
        UserCacheBasic userCache = authService.getUserCacheBasic();
        Activity activity = activityService.findById(userCache.getOrgId(), id);
        if (activity == null) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_NOT_EXIST);
        }

        PrjDetailVO prjDetailVO = new PrjDetailVO();
        BeanHelper.copyProperties(activity, prjDetailVO);

        AomUserStatisDataDTO aomUserStatisDataDTO = authPrjUserOverviewMapper.getUserStatisData(userCache.getOrgId(), id, userCache.getUserId());
        if (aomUserStatisDataDTO != null){
            BeanHelper.copyProperties(aomUserStatisDataDTO, prjDetailVO, new String[]{"actvCompletedStatus", "allTaskCount", "allTaskCompletedCount", "allTaskCompletedRate"});
            if (aomUserStatisDataDTO.getAllTaskCompletedRate() != null){
                prjDetailVO.setAllTaskCompletedRate(aomUserStatisDataDTO.getAllTaskCompletedRate().multiply(new BigDecimal("100")));
            }
        }
        return prjDetailVO;
    }

}
