package com.yxt.talent.rv.controller.manage.authprj.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class AuthPrjIndicatorStatisticsVO {

    @Schema(description = "一级指标id")
    private String firstIndicatorId;
    @Schema(description = "一级指标名称")
    private String firstIndicatorName;

    @Schema(description = "二级指标id")
    private String secondIndicatorId;
    @Schema(description = "二级指标名称")
    private String secondIndicatorName;

    @Schema(description = "三级指标id")
    private String thirdIndicatorId;
    @Schema(description = "三级指标名称")
    private String thirdIndicatorName;

    @Schema(description = "总数")
    private BigDecimal scoreTotal;

    @Schema(description = "得分")
    private BigDecimal score;

}
