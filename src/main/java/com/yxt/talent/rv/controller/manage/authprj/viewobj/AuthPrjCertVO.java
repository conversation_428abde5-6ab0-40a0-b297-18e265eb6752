package com.yxt.talent.rv.controller.manage.authprj.viewobj;

import com.yxt.talent.rv.controller.manage.authprj.cmd.AuthPrjCert4Create;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "认证项目-证书")
public class AuthPrjCertVO extends AuthPrjCert4Create {

    @Schema(description = "认证项目-证书关系表主键")
    private String id;

    @Schema(description = "证书模板名称")
    private String certTempName;

    @Schema(description = "证书模板封面地址")
    private String certCoverUrl;

    @Schema(description = "证书有效期(月)")
    private Integer validMonth;

    @Schema(description = "是否禁用（0：否、1：是）")
    private int disabled;

    @Schema(description = "是否过期（0：未过期、1：已过期）")
    private int expired;

}
