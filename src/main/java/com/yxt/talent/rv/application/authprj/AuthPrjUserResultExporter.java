package com.yxt.talent.rv.application.authprj;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yxt.aom.base.bean.part.PartMember4List;
import com.yxt.aom.base.bean.part.PartMemberReq;
import com.yxt.aom.base.component.part.ParticipationComponent;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.base.enums.DesignerAuthorityPointEnum;
import com.yxt.aom.base.manager.common.AomDesignerManager;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.spsdfacade.bean.spsd.IndicatorDto;
import com.yxt.talent.rv.application.aom.CommonAomService;
import com.yxt.talent.rv.application.authprj.dto.UserResultDTO;
import com.yxt.talent.rv.application.xpd.common.dto.AomActvExtBO;
import com.yxt.talent.rv.application.xpd.common.dto.AomActvIndicatorDto;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthUserResultVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserIndicatorMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserIndicatorPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjRuleLevelPO;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import com.yxt.talent.rv.infrastructure.service.file.ExcelUtils;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.file.expt.FileExportSupport;
import com.yxt.talent.rv.infrastructure.service.file.expt.FileExportSupportWithWaf;
import com.yxt.talent.rv.infrastructure.service.file.expt.FileExporter;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericFileExportVO;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import jakarta.annotation.Nonnull;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.null2Blank;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil.dateToString;
import static com.yxt.talent.rv.infrastructure.service.file.FileConstants.FILE_SUFFIX_XLSX;
import static com.yxt.talent.rv.infrastructure.service.file.FileConstants.SHEET_1;
import static java.lang.String.format;
import static java.util.concurrent.CompletableFuture.supplyAsync;

@Slf4j
@Component
@RequiredArgsConstructor
public class AuthPrjUserResultExporter extends FileExporter {

    // @formatter:off
    private static final String LK_AUTHPRJ_USER_RESULT_EXPT = "sprv:lk:authprj:user:result:expt:%s:%s";
    private static final String TASK_NAME = "apis.sptalentrv.authprj.user_result.export.file.name";
    private static final String HEADER_PREFIX = "apis.sptalentrv.authprj.user_result.export.sheet1.";

    private static final String[] HEADER_KEYS = {
        "fullname", "username", "status", "dept", "pos", "grade", "result", "score"
    };

    private final I18nComponent i18nComponent;
    private final I18nTranslator i18nTranslator;
    private final SpsdAclService spsdAclService;
    private final CommonAomService commonAomService;
    private final AuthService authService;
    private final Executor wafTaskExecutor;

    private final AuthprjMapper authprjMapper;
    private final AomDesignerManager designerManager;
    private final ParticipationComponent participationComponent;
    private final AuthPrjUserAppService authPrjUserAppService;
    private final AuthprjResultUserIndicatorMapper authprjResultUserIndicatorMapper;
    // @formatter:on

    /**
     * 导出用户的盘点结果明细
     *
     * @param authPrjId
     * @param req
     * @return
     */
    public void export(HttpServletRequest request, String authPrjId, PartMemberReq req) {
        UserCacheDetail currentUser = authService.getUserCacheDetail();
        String orgId = currentUser.getOrgId();
        String operator = currentUser.getUserId();
        AuthprjPO authprjPO = authprjMapper.selectById(authPrjId);
        Validate.isNotNull(authprjPO, ExceptionKeys.AUTHPRJ_NOT_EXIST);

        // extractUserIds
        List<AuthUserResultVO> userList = extractUserIds(request, req);
        if (CollectionUtils.isEmpty(userList)) {
            throw new ApiException(ExceptionKeys.PARTICIPANT_NOT_EXIST);
        }

        // 组织导出数据
        DynamicExcelExportContent exportContent = buildExportContent(authprjPO, currentUser, userList);

        FileExportSupport fileExportSupport = FileExportSupportWithWaf.builder()
            .orgId(orgId)
            .operator(operator)
            .fileName(getFileName())
            .tranId(format(LK_AUTHPRJ_USER_RESULT_EXPT, orgId, authprjPO.getId()))
            .outputStrategy(generateOutputStrategy())
            .exportContent(exportContent)
            .build();

        GenericFileExportVO export = toExport(fileExportSupport);
        log.debug("LOG20593:{}", export);
    }

    // 遍历查询分页接口，获取所有的人员id
    private List<AuthUserResultVO> extractUserIds(HttpServletRequest request, PartMemberReq req) {
        UserCacheDetail userInfo = authService.getUserCacheDetail(request);
        List<AuthUserResultVO> userList = new ArrayList<>();
        AomDataSourceTypeHolder.set(req.getRegId());
        long size = 5000;
        long current = 1;
        designerManager.checkDesignerAuthCodes(userInfo, req.getRegId(), DesignerAuthorityPointEnum.USER_READ);
        while (true) {
            Page<PartMember4List> pageable = new Page<>(current, size);
            PagingList<PartMember4List> pagingList =
                participationComponent.listPagePartMember(pageable, userInfo.getOrgId(), req,
                    userInfo.getSourceCode());
            if (CollectionUtils.isEmpty(pagingList.getDatas())) {
                break;
            }
            userList.addAll(
                BeanCopierUtil.convertList(pagingList.getDatas(), PartMember4List.class, AuthUserResultVO.class));
            current++;
        }
        return userList;
    }

    private DynamicExcelExportContent buildExportContent(
        AuthprjPO authprjPO, UserCacheDetail currentUser, List<AuthUserResultVO> userInfos) {
        DynamicExcelExportContent content = new DynamicExcelExportContent();

        String orgId = authprjPO.getOrgId();
        String authprjPOId = authprjPO.getId();
        String aomPrjId = authprjPO.getAomPrjId();

        List<String> userIds = StreamUtil.mapList(userInfos, AuthUserResultVO::getUserId);

        // 并行查询
        CompletableFuture<Map<String, UserResultDTO>> userResultsFuture = supplyAsync(
            () ->
                authPrjUserAppService.getUserResult(orgId, authprjPOId, userIds),
            wafTaskExecutor
        );

        CompletableFuture<List<AuthprjResultUserIndicatorPO>> userIndicatorResultsFuture = supplyAsync(
            () ->
                authprjResultUserIndicatorMapper.selectByActvRefIdAndUserIds(orgId, authprjPOId, userIds),
            wafTaskExecutor
        );

        CompletableFuture<List<IndicatorDto>> indicatorsFuture = supplyAsync(
            () ->
                getIndicators(orgId, aomPrjId, authprjPO.getModelId()),
            wafTaskExecutor
        );

        CompletableFuture<List<ActivityArrangeItem>> activityListFuture = supplyAsync(
            () ->
                commonAomService.activityList(orgId, aomPrjId),
            wafTaskExecutor
        );


        // 等待所有查询完成
        CompletableFuture.allOf(
            userResultsFuture, userIndicatorResultsFuture, indicatorsFuture, activityListFuture
        ).join();

        Map<String, UserResultDTO> userResults = userResultsFuture.join();
        List<AuthprjResultUserIndicatorPO> userIndicatorResults = userIndicatorResultsFuture.join();
        List<IndicatorDto> indicators = indicatorsFuture.join();
        List<ActivityArrangeItem> activityList = activityListFuture.join();

        AuthPrjUserResultExporter.ExportContext context = ExportContext.builder()
            ._super_(AuthPrjUserResultExporter.this)
            .userCacheDetail(currentUser)
            .orgId(orgId)
            .authPrjId(authprjPOId)
            .activityList(activityList)
            .userInfos(userInfos)
            .modelId(authprjPO.getModelId())
            .indicators(indicators)
            .userResults(userResults)
            .userIndicatorResults(userIndicatorResults)
            .build();
        context.init();

        content.setHeaders(initHeaders(context));
        content.setSheets(buildSingleSheets(SHEET_1, SHEET_1));
        content.setData(initDatas(context));
        return content;
    }

    private List<IndicatorDto> getIndicators(String orgId, String aomPrjId, String modelId) {
        List<AomActvExtBO> activityList = commonAomService.activityIndicatorList(orgId, aomPrjId);

        if (CollectionUtils.isEmpty(activityList)) {
            log.info("认证项目下没有活动指标信息: orgId={},  aomPrjId={}",
                orgId, aomPrjId);
            return null;
        }

        // 收集所有指标ID
        Set<String> indicatorIds = collectIndicatorIds(activityList);
        if (indicatorIds.isEmpty()) {
            log.info("认证项目下没有指标信息: orgId={}, authPrjId={}", orgId, aomPrjId);
            return null;
        }

        // 获取模型信息
        List<IndicatorDto> indicators = spsdAclService.getLastIndicators(orgId, modelId);

        // 取indicators 和 indicatorIds 的交集，最终返回IndicatorDto 实体
        return indicators.stream()
            .filter(indicator -> indicatorIds.contains(indicator.getId()))
            .collect(Collectors.toList());
    }

    public Set<String> collectIndicatorIds(List<AomActvExtBO> activityList) {
        Set<String> indicatorIds = new HashSet<>();
        for (AomActvExtBO activity : activityList) {
            if (CollectionUtils.isNotEmpty(activity.getIndicators())) {
                for (AomActvIndicatorDto indicator : activity.getIndicators()) {
                    if (StringUtils.isNotBlank(indicator.getSdIndicatorId())) {
                        indicatorIds.add(indicator.getSdIndicatorId());
                    }
                }
            }
        }
        return indicatorIds;
    }

    private OutputStrategy generateOutputStrategy() {
        return new AbstractExportStrategy() {
            @Override
            public String write(String path, String fileName, Object data) throws IOException {
                String filePath = path + fileName;
                DynamicExcelExportContent results = (DynamicExcelExportContent) data;
                ExcelUtils.exportWithDynamicHeader(
                    results.getHeaders(), results.getSheets(), results.getData(), filePath);
                return fileName;
            }

            @Override
            public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
                String taskName = AuthPrjUserResultExporter.this.i18nComponent.getI18nValue(TASK_NAME);
                return buildDownInfo(userCache, fileName, taskName);
            }
        };
    }

    private String getFileName() {
        String fileName = i18nComponent.getI18nValue(TASK_NAME);
        String dateSuffix = dateToString(new Date(), FORMATTER_YYYY_MM_DD_HH_MM_SS);
        return format("%s-%s%s", fileName, dateSuffix, FILE_SUFFIX_XLSX);
    }

    private Map<String, List<Object>> initDatas(ExportContext context) {
        Map<String, List<Object>> results = new HashMap<>();
        List<Object> rows = context.getRows();
        results.put(SHEET_1, rows);
        return results;
    }

    private Map<String, List<List<String>>> initHeaders(ExportContext context) {
        Map<String, List<List<String>>> results = new LinkedHashMap<>();
        List<List<String>> headers = new ArrayList<>();
        // 处理固定表头
        dealFixedHeads(headers);
        // 处理动态表头
        headers.addAll(context.getDyncHeaders());
        results.put(SHEET_1, headers);
        return results;
    }

    private void dealFixedHeads(List<List<String>> headers) {
        for (String key : HEADER_KEYS) {
            List<String> fixedHeaders = new ArrayList<>();
            fixedHeaders.add(i18nComponent.getI18nValue(HEADER_PREFIX + key));
            headers.add(fixedHeaders);
        }
    }

    @Builder
    private static class ExportContext {
        @Nonnull
        AuthPrjUserResultExporter _super_;
        @Nonnull
        private UserCacheDetail userCacheDetail;
        @Nonnull
        String orgId;
        @Nonnull
        String authPrjId;
        @Nonnull
        String modelId;
        @Nonnull
        List<AuthUserResultVO> userInfos;
        @Nonnull
        List<IndicatorDto> indicators;
        @Nonnull
        List<ActivityArrangeItem> activityList;
        @Nonnull
        Map<String, UserResultDTO> userResults;  // 用户结果
        @Nonnull
        List<AuthprjResultUserIndicatorPO> userIndicatorResults; // 用户指标结果

        public void init() {
            initI18n();
        }

        public List<Object> getRows() {
            List<Object> rows = new ArrayList<>();
            // 用户指标结果
            Map<Pair<String, String>, List<AuthprjResultUserIndicatorPO>> userIndicatorResultMap =
                userIndicatorResults.stream()
                    .collect(Collectors.groupingBy(e -> Pair.of(e.getUserId(), e.getSdIndicatorId())));

            for (AuthUserResultVO userResultVO : userInfos) {
                List<Object> row = new ArrayList<>();
                rows.add(row);

                String userId = userResultVO.getUserId();
                row.add(null2Blank(userResultVO.getFullname()));
                row.add(null2Blank(userResultVO.getUsername()));
                row.add(null2Blank(buildAccountStatus(userResultVO.getStatus())));
                row.add(null2Blank(userResultVO.getDeptName()));
                row.add(null2Blank(userResultVO.getPositionList()));
                row.add(null2Blank(userResultVO.getGradeName()));

                // 结果数据
                UserResultDTO authprjRuleLevelPO = userResults.get(userId);
                if (authprjRuleLevelPO != null) {
                    row.add(authprjRuleLevelPO.getLevelName());
                    row.add(authprjRuleLevelPO.getScore().stripTrailingZeros().toPlainString());
                } else {
                    row.add("--");
                    row.add("--");
                }

                // 指标结果数据
                for (IndicatorDto indicator : indicators) {
                    List<AuthprjResultUserIndicatorPO> authprjResultUserIndicatorPOS =
                        userIndicatorResultMap.get(Pair.of(userId, indicator.getId()));
                    if (CollectionUtils.isNotEmpty(authprjResultUserIndicatorPOS)) {
                        // 如果存在多个指标结果，取activityList 里的创建时间较早的活动ID作为指标结果
                        List<String> sourceIds = authprjResultUserIndicatorPOS.stream().map(e -> e.getSourceId()).collect(
                            Collectors.toList());
                        ActivityArrangeItem sourceItem = activityList.stream().filter(e -> sourceIds.contains(e.getRefId())).max(Comparator.comparing(o -> o.getCreateTime())).orElse(null);
                        if (sourceItem != null) {
                            AuthprjResultUserIndicatorPO indicatorResult = authprjResultUserIndicatorPOS.stream().filter(e -> e.getSourceId().equals(sourceItem.getRefId())).findFirst().orElse(null);
                            row.add(indicatorResult != null ? indicatorResult.getScore() : "--");
                        }
                    } else {
                        row.add("--");
                    }
                }
            }
            return rows;
        }

        public List<List<String>> getDyncHeaders() {
            List<List<String>> dyncHeaders = new ArrayList<>();
            if (!indicators.isEmpty()) {
                // 聚合所有维度组
                dyncHeaders.addAll(indicators
                    .stream()
                    .map(IndicatorDto::getIndicatorName)
                    .map(Lists::newArrayList)
                    .toList());
            }

            return dyncHeaders;
        }

        private String buildAccountStatus(Integer status) {
            return status == 2 ? "删除" : status == 1 ? "启用" : "禁用";
        }

        public void initI18n() {
            String userLang = userCacheDetail.getLocale();
            _super_.i18nTranslator.translate(orgId, userLang, userInfos);
        }

    }

}
