package com.yxt.talent.rv.application.authprj;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.application.activity.RvAomActivityService;
import com.yxt.talent.rv.application.todo.AuthPrjStrategy;
import com.yxt.talent.rv.application.todo.TodoSenderComponent;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvActivityParticipationMemberPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import com.yxt.talent.rv.infrastructure.service.remote.MessageAclService;
import com.yxt.talent.rv.infrastructure.service.remote.dto.MessageDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class AuthPrjTodoAndMsgService {
    public static final String AUTH_URL_PC = "/sp/spgwnl/#/web/certificate/detail?projectId=";
    public static final String AUTH_URL_H5 = "/m/spgwnl/#/certificate/details?id=";
    private final CoreAclService coreAclService;
    private final AuthprjMapper authprjMapper;
    private final ActivityService activityService;
    private final RvAomActivityService aomActivityService;
    private final ActivityParticipationService activityParticipationService;
    private final MessageAclService messageAclService;
    private static final String ENDFAIL_NOTIFY = "certificate_end_manager";
    /**
     * 创建空待办
     *
     * @param orgId
     * @param opUserId
     * @param authPrjId
     */
    @Async
    public void createEmptyTodo(String orgId, String opUserId, String authPrjId) {
        log.info("认证待办-空待办创建：{}", authPrjId);
        Activity activity = getActivity(orgId, authPrjId);
        // 6.4 创建空的认证项目的待办，在认证项目创建的时候使用
        AuthPrjStrategy.AuthPrjTodoInfoDto authPrjTodoInfoDto = new AuthPrjStrategy.AuthPrjTodoInfoDto();
        authPrjTodoInfoDto.setPrjName(activity.getActvName());
        authPrjTodoInfoDto.setStartTime(DateUtil.toLocalDateTime(activity.getStartTime()));
        authPrjTodoInfoDto.setEndTime(DateUtil.toLocalDateTime(activity.getEndTime()));
        authPrjTodoInfoDto.setAuthPrjId(authPrjId);
        String url = getUrl(orgId, activity);
        // 设置跳转url
        authPrjTodoInfoDto.setUrl(url);
        try {
            TodoSenderComponent.getAuthPrjStrategy().createEmptyTodo(orgId, opUserId, authPrjId, authPrjTodoInfoDto);
        } catch (Exception e) {
            log.error("待办异常 createEmptyTodo error. first:{}", authPrjId, e);
        }
    }

    private String getUrl(String orgId, Activity activity) {
        String pcUrl = AUTH_URL_PC + activity.getId();
        String h5Url = AUTH_URL_H5 + activity.getId();
        return coreAclService.getScanentryURL(orgId, pcUrl, h5Url);
    }

    @Async
    public void createTodo(String orgId, String opUserId, String authPrjId) {
        log.info("认证待办-待办创建+加人：{}", authPrjId);
        createEmptyTodo(orgId, opUserId, authPrjId);
        Activity activity = getActivity(orgId, authPrjId);
        if (activity.getActvStatus() != 2){
            log.info("活动状态不在进行中，authPrjId:{}", authPrjId);
            return;
        }
        dealUser(orgId, authPrjId, (users) -> {
            if (CollectionUtils.isNotEmpty(users)){
                List<String> uids = StreamUtil.mapList(users, RvActivityParticipationMemberPO::getUserId);
                addUserTodo(orgId, opUserId, authPrjId, uids);
            }
        });
    }

    @Async
    public void addUserTodo(String orgId, String opUserId, String authPrjId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        Activity activity = getActivity(orgId, authPrjId);
        if (activity.getActvStatus() != 2){
            log.info("活动状态不为进行中，authPrjId:{}", authPrjId);
            return;
        }
        String actvId = activity.getId();
        // 6.4 添加认证项目下，指定人员的待办，添加人员的时候使用
        log.info(
            "认证待办-待办加人：{}, userIds:{}", authPrjId, BeanHelper.bean2Json(userIds, JsonInclude.Include.NON_NULL));
        try {
            List<RvActivityParticipationMemberPO> list = aomActivityService.getPrjUsersByIds(orgId, actvId, userIds);
            AuthPrjStrategy.AuthPrjTodoInfoDto authPrjTodoInfoDto = new AuthPrjStrategy.AuthPrjTodoInfoDto();
            authPrjTodoInfoDto.setAuthPrjId(authPrjId);
            authPrjTodoInfoDto.setUsers(list);
            TodoSenderComponent.getAuthPrjStrategy().addTodoUser(orgId, opUserId, authPrjId, authPrjTodoInfoDto);
        } catch (Exception e) {
            log.error("待办异常 addUserTodo error. first:{}", authPrjId, e);
        }
    }

    /**
     * 修改项目信息
     *
     * @param orgId
     * @param opUserId
     * @param authPrjId
     * @param changeTime 是否修改时间
     */
    public void changeTodo(String orgId, String opUserId, String authPrjId, boolean changeTime) {
        // 6.4 修改认证项目的待办，修改项目的时候使用
        log.info("认证待办-修改项目信息：{}", authPrjId);
        try {
            Activity activity = getActivity(orgId, authPrjId);
            if (activity.getActvStatus() != 2) {
                // 只有进行中的项目才会发待办，才需要修改
                return;
            }
            AuthPrjStrategy.AuthPrjTodoInfoDto authPrjTodoInfoDto = new AuthPrjStrategy.AuthPrjTodoInfoDto();
            authPrjTodoInfoDto.setPrjName(activity.getActvName());
            authPrjTodoInfoDto.setStartTime(DateUtil.toLocalDateTime(activity.getStartTime()));
            authPrjTodoInfoDto.setEndTime(DateUtil.toLocalDateTime(activity.getEndTime()));
            authPrjTodoInfoDto.setAuthPrjId(authPrjId);
            String url = getUrl(orgId, activity);
            // 设置跳转url
            authPrjTodoInfoDto.setUrl(url);
            TodoSenderComponent.getAuthPrjStrategy().modifyTodoInfo(orgId, opUserId, authPrjTodoInfoDto);
            if (activity.getTimeModel() == 1 && changeTime) {
                // 相对时间, 并且修改了时间, 需要把所有用户重新修改
                dealUser(orgId, authPrjId, (users) -> {
//                    AuthPrjStrategy.AuthPrjTodoInfoDto authPrjUser = new AuthPrjStrategy.AuthPrjTodoInfoDto();
//                    authPrjUser.setAuthPrjId(authPrjId);
//                    authPrjUser.setUsers(users);
//                    TodoSenderComponent.getAuthPrjStrategy().editTodoUser(orgId, opUserId, authPrjId, authPrjUser);
                    if (CollectionUtils.isNotEmpty(users)) {
                        List<String> uids = StreamUtil.mapList(users, RvActivityParticipationMemberPO::getUserId);
                        deleteUserTodo(orgId, opUserId, authPrjId, uids);
                        addUserTodo(orgId, opUserId, authPrjId, uids);
                    }
                });
            }
        } catch (Exception e) {
            log.error("待办异常 changeTodo. first:{}", authPrjId, e);
        }
    }

    @Async
    public void delAllUserTodo(String orgId, String opUserId, String authPrjId) {
        // 删除认证项目下，所有人员的待办，结束项目时使用
        log.info("认证待办-删除所有人员：{}", authPrjId);
        try {
            dealUser(orgId, authPrjId, (users) -> {
                List<String> userIds = users.stream().map(RvActivityParticipationMemberPO::getUserId).toList();
                TodoSenderComponent.getAuthPrjStrategy().removeTodoUser(orgId, opUserId, authPrjId, userIds);
            });
        } catch (Exception e) {
            log.error("待办异常 delAllUserTodo. first:{}", authPrjId, e);
        }
    }

    @Async
    public void deleteUserTodo(String orgId, String opUserId, String authPrjId, List<String> userIds) {
        // 6.4 删除指定人员的待办 , 删除人员的时候使用
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        log.info(
            "认证待办-删除人员待办 authPrjId:{}, userIds:{}", authPrjId,
            BeanHelper.bean2Json(userIds, JsonInclude.Include.NON_NULL));
        try {
            log.info("deleteUserTodo authPrjId:{}", authPrjId);
            TodoSenderComponent.getAuthPrjStrategy().removeTodoUser(orgId, opUserId, authPrjId, userIds);
        } catch (Exception e) {
            log.error("待办异常 deleteUserTodo error. first:{}", authPrjId, e);
        }
    }

    @Async
    public void delTodo(String orgId, String opUserId, String authPrjId) {
        // 6.4 删除认证项目的待办，删除项目的时候使用
        log.info("认证待办-删除项目：{}", authPrjId);
        try {
            log.info("delTodo authPrjId:{}", authPrjId);
            TodoSenderComponent.getAuthPrjStrategy().deleteTodos(orgId, opUserId, authPrjId);
        } catch (Exception e) {
            log.error("待办异常 delTodo error. first:{}", authPrjId, e);
        }
    }

    @Async
    public void finishUserTodo(String orgId, String opUserId, String authPrjId, List<String> userIds) {
        // 6.4 完成待办
        log.info(
            "认证待办-完成人员：{}, userIds:{}", authPrjId, BeanHelper.bean2Json(userIds, JsonInclude.Include.NON_NULL));
        try {
            log.info("finishUserTodo authPrjId:{}", authPrjId);
            if (CollectionUtils.isEmpty(userIds)) {
                return;
            }
            TodoSenderComponent.getAuthPrjStrategy().doneTodos(orgId, opUserId, Map.of(authPrjId, userIds));
        } catch (Exception e) {
            log.error("待办异常 finishUserTodo error. first:{}", authPrjId, e);
        }
    }

    public Activity getActivity(String orgId, String authPrjId) {
        AuthprjPO authprjPO = authprjMapper.selectByIdAndOrg(orgId, authPrjId);
        Validate.isNotNull(authprjPO, ExceptionKeys.AUTHPRJ_NOT_EXIST);
        Activity activity = activityService.findById(orgId, authprjPO.getAomPrjId());
        return Validate.isNotNull(activity, ExceptionKeys.AUTHPRJ_NOT_EXIST);
    }

    public void dealUser(String orgId, String authPrjId, Consumer<List<RvActivityParticipationMemberPO>> consumer) {
        AuthprjPO authprjPO = authprjMapper.selectByIdAndOrg(orgId, authPrjId);
        if (authprjPO == null) {
            log.info("authprjPO is null, authPrjId:{}", authPrjId);
            return;
        }
        String actvId = authprjPO.getAomPrjId();
        int size = 1000;
        int current = 1;
        boolean flag = true;
        while (flag) {
            Page<RvActivityParticipationMemberPO> page = new Page<>(current, size);
            PagingList<RvActivityParticipationMemberPO> rvActivityParticipationMemberPOPagingList =
                aomActivityService.getPrjUsers(page, orgId, actvId);
            // 业务处理
            if (rvActivityParticipationMemberPOPagingList != null &&
                CollectionUtils.isNotEmpty(rvActivityParticipationMemberPOPagingList.getDatas())) {
                consumer.accept(rvActivityParticipationMemberPOPagingList.getDatas());
            }
            if (rvActivityParticipationMemberPOPagingList != null &&
                CollectionUtils.isNotEmpty(rvActivityParticipationMemberPOPagingList.getDatas()) &&
                rvActivityParticipationMemberPOPagingList.getDatas().size() >= size) {
                current++;
                continue;
            }
            flag = false;
        }
    }

    /**
     * 项目结束通知
     * @param orgId
     * @param opUserId
     * @param aomActvId
     * @return
     */
    public void closeProjectNotice(String orgId, String opUserId, String aomActvId){
        Activity activity = activityService.findById(orgId, aomActvId);
        Long partId = activityParticipationService.getParticipationId(orgId, aomActvId);
        List<IdName> list = activityService.listActivityMgr(orgId, aomActvId, partId);
        log.info("项目负责人，{}, partId:{}, aomActvId:{}", BeanHelper.bean2Json(list, JsonInclude.Include.NON_NULL), partId, aomActvId);

        try {
            if (CollectionUtils.isNotEmpty(list)){
                List<String> mgrUserIds = list.stream().map(IdName::getId).collect(Collectors.toList());
                MessageDTO messageDTO = new MessageDTO();
                messageDTO.setTemplateCode(ENDFAIL_NOTIFY);
                messageDTO.setOrgId(orgId);
                messageDTO.setUserId(opUserId);
                messageDTO.setUserIds(mgrUserIds);
                HashMap<String, String> placeholderMap = new HashMap<>(8);
                placeholderMap.put("{{projectName}}", activity.getActvName());
                String url = getUrl(orgId, activity);
                placeholderMap.put("{{url}}", url);
                if (activity.getStartTime() != null){
                    placeholderMap.put("{{startTime}}", LocalDateTimeUtil.format(DateUtil.toLocalDateTime(activity.getStartTime()), "yyyy-MM-dd HH:mm"));
                }
                if (activity.getEndTime() != null){
                    placeholderMap.put("{{endTime}}", LocalDateTimeUtil.format(DateUtil.toLocalDateTime(activity.getEndTime()), "yyyy-MM-dd HH:mm"));
                }
                messageDTO.setPlaceholderMap(placeholderMap);
                // 发送消息
                log.info("发送消息，{}", BeanHelper.bean2Json(messageDTO, JsonInclude.Include.NON_NULL));
                messageAclService.sendTemplateMessage(messageDTO);
            }
        }catch (Exception e){
            log.error("项目结束通知异常", e);
        }
    }

}
