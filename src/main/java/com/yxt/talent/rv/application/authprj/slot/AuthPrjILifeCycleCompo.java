package com.yxt.talent.rv.application.authprj.slot;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.base.bean.part.cycle.*;
import com.yxt.aom.base.custom.ILifeCycleCompo;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.authprj.AuthPrjCalcAppService;
import com.yxt.talent.rv.application.authprj.AuthPrjTodoAndMsgService;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.MQConstant;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import com.yxt.talent.rv.infrastructure.trigger.message.rocket.authprj.AuthPrjDelMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 认证项目生命周期自定义接口
 */
@Slf4j
@RequiredArgsConstructor
@Component("iLifeCycleCompo4ProjAuthpj")
public class AuthPrjILifeCycleCompo implements ILifeCycleCompo {
    private final AuthprjMapper authprjMapper;
    private final AuthPrjTodoAndMsgService authPrjTodoService;
    private final AuthPrjCalcAppService authPrjCalcAppService;
    private final RocketMqAclSender rocketMqAclSender;

    @Override
    @Auditing
    public void release(ActivityReleasePostProcessorThird activityStatusChangeThird) {
        log.info("认证项目发布,{}", BeanHelper.bean2Json(activityStatusChangeThird, JsonInclude.Include.NON_NULL));
        String orgId = activityStatusChangeThird.getOrgId();
        if (CollectionUtils.isNotEmpty(activityStatusChangeThird.getRefIds())) {
            for (String aomActvId : activityStatusChangeThird.getRefIds()) {
                AuthprjPO authprjPO = authprjMapper.selectByAomPrjId(orgId, aomActvId);
                if (authprjPO == null) {
                    throw new ApiException(ExceptionKeys.AUTHPRJ_NOT_EXIST);
                }
                //6.4 创建认证员工待办, 创建项目时也会创建待办
                authPrjTodoService.createTodo(orgId, activityStatusChangeThird.getUserId(), authprjPO.getId());
            }
        }

    }

    @Override
    public void withdraw(ActivityWithdrawPostProcessorThird activityStatusChangeThird) {

    }

    @Override
    @Auditing
    public void close(ActivityEndPostProcessorThird activityEndPostProcessorThird) {
        log.info("项目结束,{}", BeanHelper.bean2Json(activityEndPostProcessorThird, JsonInclude.Include.NON_NULL));
        // 结束项目，触发项目计算
        String orgId = activityEndPostProcessorThird.getOrgId();
        String opUserId = activityEndPostProcessorThird.getUserId();
        List<String> authPrjIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(activityEndPostProcessorThird.getRefIds())) {
            for (String aomActvId : activityEndPostProcessorThird.getRefIds()) {
                AuthprjPO authPrjPO = authprjMapper.selectByAomPrjId(orgId, aomActvId);
                if (authPrjPO == null) {
                    throw new ApiException(ExceptionKeys.AUTHPRJ_NOT_EXIST);
                }
                authPrjIds.add(authPrjPO.getId());
                authPrjTodoService.closeProjectNotice(orgId, opUserId, aomActvId);
            }
        }

        try {
            if (CollectionUtils.isNotEmpty(authPrjIds)) {
                log.info("删除认证项目待办 start,{}", BeanHelper.bean2Json(authPrjIds, JsonInclude.Include.NON_NULL));
                authPrjIds.forEach(authPrjId -> {
                    //  项目结束，进行项目计算
                    authPrjCalcAppService.triggerProjectCalc(orgId, authPrjId, false);
                    authPrjTodoService.delTodo(orgId, opUserId, authPrjId);
                });
                log.info("删除认证项目待办 end,{}", BeanHelper.bean2Json(authPrjIds, JsonInclude.Include.NON_NULL));
            }
        } catch (Exception e) {
            log.error("删除认证项目待办失败,{}", BeanHelper.bean2Json(authPrjIds, JsonInclude.Include.NON_NULL));
        }

    }

    @Override
    public void archive(ActivityArchivePostProcessorThird activityStatusChangeThird) {

    }

    @Override
    @Auditing
    public void del(ActivityDelPostProcessorThird activityStatusChangeThird) {
        String orgId = activityStatusChangeThird.getOrgId();
        String userId = activityStatusChangeThird.getUserId();
        // 删除项目主表
        if (StringUtils.isNotBlank(activityStatusChangeThird.getOrgId()) &&
            CollectionUtils.isNotEmpty(activityStatusChangeThird.getRefIds())) {

            for (String aomActvId : activityStatusChangeThird.getRefIds()) {
                AuthprjPO authprjPO = authprjMapper.selectByAomPrjId(orgId, aomActvId);
                if (authprjPO == null) {
                    throw new ApiException(ExceptionKeys.AUTHPRJ_NOT_EXIST);
                }

                AuthPrjDelMsg authPrjDelMsg = new AuthPrjDelMsg();
                authPrjDelMsg.setOrgId(orgId);
                authPrjDelMsg.setUserId(userId);
                authPrjDelMsg.setAuthPrjId(authprjPO.getId());
                rocketMqAclSender.send(MQConstant.TOPIC_AUTH_PRJ_DELETE, BeanHelper.bean2Json(authPrjDelMsg, JsonInclude.Include.NON_NULL));

                authPrjTodoService.delTodo(orgId, userId, authprjPO.getId());
            }

            authprjMapper.deleteByAomPrjIds(activityStatusChangeThird.getOrgId(), activityStatusChangeThird.getRefIds());
        }
    }

}
