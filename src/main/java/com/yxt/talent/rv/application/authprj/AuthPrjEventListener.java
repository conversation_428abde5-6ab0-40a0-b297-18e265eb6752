package com.yxt.talent.rv.application.authprj;

import com.yxt.aom.base.bean.control.MemberStatisticsChangeMsg;
import com.yxt.event.EventListener;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.infrastructure.trigger.message.rocket.activity.AomMemberStatisticChangeEvent;
import com.yxt.talent.rv.infrastructure.trigger.task.xxljob.authprj.event.AuthPrjCertRemindTaskEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 认证项目事件监听器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthPrjEventListener implements EventListener {

    private final AuthPrjCertRemindService authPrjCertRemindService;
    private final AuthPrjCalcAppService authPrjCalcAppService;

    /**
     * 处理证书到期提醒任务事件
     */
    @org.springframework.context.event.EventListener
    public void handleAuthPrjCertRemindTaskEvent(AuthPrjCertRemindTaskEvent event) {
        try {
            log.info("开始处理证书到期提醒任务事件: {}", event);
            authPrjCertRemindService.processCertRemindTask(event.getShardingVo());
            log.info("证书到期提醒任务事件处理完成");
        } catch (Exception e) {
            log.error("处理证书到期提醒任务事件失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理用户认证结果事件
     */
    @org.springframework.context.event.EventListener
    public void handleAomMemberStatisticChangeEvent(AomMemberStatisticChangeEvent event) {
        try {
            MemberStatisticsChangeMsg message = event.getMessage();
            if (Objects.equals(message.getActvRegId(), UacdTypeEnum.PRJ_AUTH.getRegId())) {
                log.info("开始处理aom活动进度发生变化事件: {}", event);

                authPrjCalcAppService.dealWhenAomMemberStatisticChange(message.getOrgId(), message.getUserId(), message.getActvId());

                log.info("aom活动进度发生变化事件处理完成");
            }
        } catch (Exception e) {
            log.error("处理aom活动进度发生变化事件失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
}
