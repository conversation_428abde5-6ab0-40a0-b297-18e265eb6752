package com.yxt.talent.rv.application.authprj;

import com.yxt.common.util.Validate;
import com.yxt.talent.rv.application.authprj.AuthPrjIndicatorHierarchyService.IndicatorDataConverter;
import com.yxt.talent.rv.application.authprj.AuthPrjIndicatorHierarchyService.IndicatorProcessContext;
import com.yxt.talent.rv.application.authprj.dto.AuthPrjDimGroupDTO;
import com.yxt.talent.rv.application.authprj.dto.AuthPrjIndicatorDetailDTO;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjIndicatorStatisticsVO;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjOverviewStatisticsVO;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthprjDimGroupStatisticsVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjOverviewMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 认证概览
 */
@Slf4j
@Service
@AllArgsConstructor
public class AuthPrjOverviewAppService {

    private final AuthprjOverviewMapper authprjOverviewMapper;
    private final AuthPrjIndicatorHierarchyService hierarchyService;
    private final Executor wafTaskExecutor;
    private final AuthprjMapper authprjMapper;

    /**
     * 获取认证项目统计数据（异步并行）
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @return 统计数据列表
     */
    public AuthPrjOverviewStatisticsVO getStatistics(String orgId, String authprjId) {
        AuthprjPO authprjPO = authprjMapper.selectById(authprjId);
        Validate.isNotNull(authprjPO, ExceptionKeys.AUTHPRJ_NOT_EXIST);
        CompletableFuture<Integer> participantCountFuture =
            CompletableFuture.supplyAsync(() -> authprjOverviewMapper.countParticipants(orgId, authprjId), wafTaskExecutor);
        CompletableFuture<Integer> completedCountFuture =
            CompletableFuture.supplyAsync(() -> authprjOverviewMapper.countCompleted(orgId, authprjId), wafTaskExecutor);
        CompletableFuture<Integer> passedCountFuture =
            CompletableFuture.supplyAsync(() -> authprjOverviewMapper.countPassed(orgId, authprjId), wafTaskExecutor);
        Integer participantCount = participantCountFuture.join();
        Integer completedCount = completedCountFuture.join();
        Integer passedCount = passedCountFuture.join();
        Integer passRate = MathUtil.calculateRate(passedCount, participantCount);
        return new AuthPrjOverviewStatisticsVO(participantCount, completedCount, passedCount, passRate);
    }

    /**
     * 获取认证指标结果统计（按维度分组）
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @return 维度分组统计数据
     */
    public List<AuthprjDimGroupStatisticsVO> getPrjIndicatorDetails(String orgId, String authprjId) {
        log.info("LOG10136:获取认证指标结果统计: orgId={}, authprjId={}", orgId, authprjId);

        AuthprjPO authprjPO = authprjMapper.selectById(authprjId);
        Validate.isNotNull(authprjPO, ExceptionKeys.AUTHPRJ_NOT_EXIST);

        // 初始化指标处理上下文
        IndicatorProcessContext context = hierarchyService.initProcessContext(orgId, authprjId);
        if (context == null) {
            return new ArrayList<>();
        }

        // 获取指标统计数据
        List<AuthPrjIndicatorStatisticsVO> indicatorStatistics =
            authprjOverviewMapper.getIndicatorStatistics(orgId, authprjId, new ArrayList<>(context.getIndicatorIds()));

        // 按维度分组
        List<AuthprjDimGroupStatisticsVO> result = groupByDimension(context, indicatorStatistics);
        log.info("认证指标结果统计获取成功: orgId={}, authprjId={}, 维度分组数量={}", orgId, authprjId, result.size());
        return result;
    }

    /**
     * 按维度分组统计数据
     */
    private List<AuthprjDimGroupStatisticsVO> groupByDimension(
            IndicatorProcessContext context, List<AuthPrjIndicatorStatisticsVO> indicatorStatistics) {

        // 创建数据转换器
        IndicatorDataConverter<AuthPrjIndicatorStatisticsVO> converter = new IndicatorDataConverter<>() {
            @Override
            public AuthPrjIndicatorDetailDTO convertToDTO(AuthPrjIndicatorStatisticsVO source) {
                AuthPrjIndicatorDetailDTO dto = new AuthPrjIndicatorDetailDTO();
                BeanUtils.copyProperties(source, dto);
                return dto;
            }

            @Override
            public String getOriginalIndicatorId(AuthPrjIndicatorStatisticsVO source) {
                return source.getFirstIndicatorId();
            }
        };

        // 使用通用方法进行分组处理
        List<AuthPrjDimGroupDTO> commonResult = hierarchyService.groupIndicatorsByDimension(context, indicatorStatistics, converter);

        // 转换为特定的返回类型
        return commonResult.stream().map(this::convertToSpecificVO).collect(Collectors.toList());
    }

    /**
     * 将通用DTO转换为特定的VO
     */
    private AuthprjDimGroupStatisticsVO convertToSpecificVO(AuthPrjDimGroupDTO dto) {
        AuthprjDimGroupStatisticsVO vo = new AuthprjDimGroupStatisticsVO();
        vo.setSdDimId(dto.getSdDimId());
        vo.setSdDimName(dto.getSdDimName());

        // 转换指标列表
        List<AuthPrjIndicatorStatisticsVO> indicatorList = dto.getIndicatorList().stream()
                .map(this::convertToIndicatorStatisticsVO)
                .collect(Collectors.toList());
        vo.setIndicatorList(indicatorList);

        return vo;
    }

    /**
     * 将通用指标DTO转换为统计VO
     */
    private AuthPrjIndicatorStatisticsVO convertToIndicatorStatisticsVO(AuthPrjIndicatorDetailDTO dto) {
        AuthPrjIndicatorStatisticsVO vo = new AuthPrjIndicatorStatisticsVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }
}
