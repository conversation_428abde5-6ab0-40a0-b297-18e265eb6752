package com.yxt.talent.rv.application.authprj;

import com.alibaba.fastjson.JSON;
import com.yxt.cerapifacade.bean.*;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.spsdk.common.bean.RuleOptionBean;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.controller.manage.authprj.cmd.AuthPrjCertIssue4Revoke;
import com.yxt.talent.rv.controller.manage.authprj.query.AuthPrjCertIssue4Query;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertIssueVO;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertVO;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationMemberMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvAssessmentActivityResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvAssessmentActivityResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.*;
import com.yxt.talent.rv.infrastructure.service.remote.CertAclService;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import com.yxt.talent.rv.infrastructure.trigger.message.rocket.cert.CertIssueMessage;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.AUTHPRJ_CERT_NOT_EXIST;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.AUTHPRJ_NOT_EXIST;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.ORG_ID_NOT_MATCH;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_CER_TEMP_ISSUE_V2;
import static java.util.Collections.singletonList;

/**
 * 认证项目证书颁发管理服务
 * 负责证书颁发、吊销、状态管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthPrjCertIssueService {

    private final AuthprjCertMapper authprjCertMapper;
    private final CertLogMapper certLogMapper;
    private final CertAclService certAclService;
    private final RocketMqAclSender rocketMqAclSender;
    private final AuthprjResultUserMapper authprjResultUserMapper;
    private final RvActivityParticipationMemberMapper rvActivityParticipationMemberMapper;
    private final RvAssessmentActivityResultMapper rvAssessmentActivityResultMapper;
    private final AuthprjRuleLevelMapper authprjRuleLevelMapper;
    private final AuthprjMapper authprjMapper;

    /**
     * 获取证书颁发记录列表
     */
    public PagingList<AuthPrjCertIssueVO> issueList(
        String orgId, String authPrjId, String certId, AuthPrjCertIssue4Query query) {
        // 验证证书存在
        AuthprjCertPO cert = authprjCertMapper.selectById(certId);
        Validate.isNotNull(cert, AUTHPRJ_CERT_NOT_EXIST);
        Validate.isTrue(orgId.equals(cert.getOrgId()) && authPrjId.equals(cert.getAuthprjId()), ORG_ID_NOT_MATCH);

        // 调用证书服务获取颁发记录
        return getCertIssueListFromService(orgId, authPrjId, cert.getCertTempId(), query);
    }

    /**
     * 颁发证书
     */
    public void issue(String orgId, String authPrjId, String certId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        // 非进行中的认证项目，不能颁证
        AuthprjPO authPrj = authprjMapper.selectByOrgIdAndId(orgId, authPrjId);
        if (authPrj == null || authPrj.getActvStatus() == null ||authPrj.getActvStatus() != 2) {
            log.debug(
                "LOG41126:orgId={},authPrjId={},authPrjActvStatus={}", orgId, authPrjId,
                authPrj != null ? authPrj.getActvStatus() : "null");
            return;
        }

        String operator = decideOperator();

        // 1. 验证证书存在
        AuthprjCertPO cert = authprjCertMapper.selectById(certId);
        Validate.isNotNull(cert, AUTHPRJ_CERT_NOT_EXIST);
        Validate.isTrue(orgId.equals(cert.getOrgId()) && authPrjId.equals(cert.getAuthprjId()), ORG_ID_NOT_MATCH);

        // 1.1 验证证书是否有效
        String certTempId = cert.getCertTempId();
        boolean isValid = verifyCertTemp(orgId, certTempId);
        if (!isValid) {
            return;
        }

        // 2. 创建证书颁发记录
        List<CertLogPO> certLogList = userIds.stream().map(userId -> {
            CertLogPO certLog = new CertLogPO();
            certLog.setId(ApiUtil.getUuid());
            certLog.setOrgId(orgId);
            certLog.setSourceId(authPrjId);
            certLog.setCertTempId(cert.getCertTempId());
            certLog.setUserId(userId);
            certLog.setCertStatus(0); // 待颁发等待证书服务回调，告知颁发结果
            certLog.setDeleted(0);
            EntityUtil.setAuditFields(certLog, operator);
            return certLog;
        }).collect(Collectors.toList());

        // 先删后增
        certLogMapper.deleteBySourceIdAndCertTempId(orgId, authPrjId, cert.getCertTempId());
        certLogMapper.batchInsertOrUpdate(certLogList);

        // 3. 发送MQ消息进行证书颁发
        sendCertIssueMessage(orgId, authPrj.getAomPrjId(), cert.getCertTempId(), userIds);
    }

    /**
     * 校验证书模板合法性
     */
    private boolean verifyCertTemp(String orgId, String certTempId) {
        if (StringUtils.isBlank(certTempId)) {
            return false;
        }

        CerReqBean cerReqBean = new CerReqBean();
        cerReqBean.setOrgId(orgId);
        cerReqBean.setIds(Collections.singletonList(certTempId));

        List<CerSimpleInfoBean> certTemplates = certAclService.getCerList(cerReqBean);
        if (CollectionUtils.isEmpty(certTemplates)) {
            return false;
        }

        CerSimpleInfoBean template = certTemplates.get(0);
        return template.getDeleted() != 1 && template.getDisabled() != 1 && template.getExpired() != 1;
    }

    /**
     * 吊销证书
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(String orgId, String authPrjId, String certId, List<AuthPrjCertIssue4Revoke> command) {
        if (CollectionUtils.isEmpty(command)) {
            return;
        }

        // 1. 验证证书存在
        AuthprjCertPO cert = authprjCertMapper.selectById(certId);
        Validate.isNotNull(cert, AUTHPRJ_CERT_NOT_EXIST);
        Validate.isTrue(orgId.equals(cert.getOrgId()) && authPrjId.equals(cert.getAuthprjId()), ORG_ID_NOT_MATCH);

        // 2. 更新本地证书状态
        List<String> userIds = StreamUtil.mapList(command, AuthPrjCertIssue4Revoke::getUserId);
        updateCertStatus(orgId, authPrjId, certId, userIds, 2);

        // 3. 调用证书服务吊销证书
        List<String> issueIds = StreamUtil.mapList(command, AuthPrjCertIssue4Revoke::getIssueId);
        revokeCertificates(orgId, cert.getCertTempId(), issueIds, decideOperator());
    }

    /**
     * 更新用户证书状态
     */
    public void updateCertStatus(
        String orgId, String sourceId, String cerId, List<String> userIds, Integer certStatus) {
        certLogMapper.updateCertStatus(orgId, sourceId, cerId, userIds, certStatus);
    }

    /**
     * 检查并自动颁发证书（批量处理版本）
     * 当员工认证结果发生变化时调用此方法
     * 
     * @param orgId 机构ID
     * @param authprjId 认证项目ID
     * @param userIds 用户ID列表
     * @param certId 证书ID，如果为null则处理所有证书
     */
    public void checkAndIssueCerts(String orgId, String authprjId, List<String> userIds, @Nullable String certId) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        
        try {
            AuthprjPO authprjPO = authprjMapper.selectById(authprjId);
            Validate.isNotNull(authprjPO, AUTHPRJ_NOT_EXIST);

            // 获取该认证项目的证书配置
            List<AuthPrjCertVO> certList = authprjCertMapper.selectCertListByAuthprjId(orgId, authprjId);
            if (StringUtils.isNotBlank(certId)) {
                certList = certList.stream()
                    .filter(cert -> certId.equals(cert.getId()))
                    .collect(Collectors.toList());
                if (certList.isEmpty()) {
                    log.warn("指定的证书不存在或不属于该认证项目: certId={}, authprjId={}", certId, authprjId);
                    return;
                }
                log.info("处理指定证书: authprjId={}, certId={}, userIdsCount={}", authprjId, certId, userIds.size());
            }

            if (CollectionUtils.isEmpty(certList)) {
                return;
            }
            
            // 为每个证书批量处理所有用户
            for (AuthPrjCertVO cert : certList) {
                try {
                    // 批量检查用户是否满足证书获取条件
                    List<String> qualifiedUsers = new ArrayList<>();
                    for (String userId : userIds) {
                        if (checkUserMeetsCertCondition(orgId, authprjPO, userId, cert)) {
                            qualifiedUsers.add(userId);
                        }
                    }

                    if (CollectionUtils.isEmpty(qualifiedUsers)) {
                        continue;
                    }

                    // 批量检查是否已经颁发过该证书
                    Map<String, Boolean> issuedStatus =
                        batchHasIssuedCert(orgId, authprjId, cert.getCertTempId(), qualifiedUsers);

                    // 筛选出未颁发证书的用户
                    List<String> usersToIssue = qualifiedUsers.stream()
                        .filter(userId -> !issuedStatus.getOrDefault(userId, false))
                        .collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(usersToIssue)) {
                        continue;
                    }

                    // 批量颁发证书
                    issue(orgId, authprjId, cert.getId(), usersToIssue);
                    log.info("批量自动颁发证书成功: authprjId={}, certId={}, usersToIssueCount={}", authprjId, cert.getId(), usersToIssue.size());
                } catch (Exception e) {
                    log.error("LOG42466:批量自动颁发证书失败: authprjId={}, userIdsCount={}, certId={}, error={}",
                        authprjId, userIds.size(), cert.getId(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("批量自动颁发证书检查失败: authprjId={}, userIdsCount={}, certId={}, error={}", 
                authprjId, userIds.size(), certId, e.getMessage(), e);
        }
    }

    /**
     * 检查并自动颁发证书
     * 当员工认证结果发生变化时调用此方法
     * 
     * @param orgId 机构ID
     * @param authprjId 认证项目ID
     * @param userId 用户ID
     * @param certId 证书ID，如果为null则处理所有证书
     */
    public void checkAndIssueCerts(String orgId, String authprjId, String userId, @Nullable String certId) {
        checkAndIssueCerts(orgId, authprjId, Collections.singletonList(userId), certId);
    }

    /**
     * 检查并自动颁发证书
     * 当员工认证结果发生变化时调用此方法
     * 
     * @param orgId 机构ID
     * @param authprjId 认证项目ID
     * @param userId 用户ID
     */
    public void checkAndIssueCerts(String orgId, String authprjId, String userId) {
        checkAndIssueCerts(orgId, authprjId, userId, null);
    }

    /**
     * 为项目下所有员工颁发证书（同步执行）
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @param certId
     */
    public void issueCerts(String orgId, String authprjId, String certId) {
        log.info("LOG42226:开始为项目下所有员工同步颁发证书: authprjId={}, certId={}", authprjId, certId);
        AuthprjPO authPrj = authprjMapper.selectById(authprjId);
        Validate.isNotNull(authPrj, AUTHPRJ_NOT_EXIST);
        List<String> userIds = rvActivityParticipationMemberMapper.findAllUserIdByActId(orgId, authPrj.getAomPrjId());

        if (CollectionUtils.isEmpty(userIds)) {
            log.info("LOG42236:项目下没有需要处理的员工: authprjId={}", authprjId);
            return;
        }

        // 使用批量处理方法替代逐个处理
        try {
            checkAndIssueCerts(orgId, authprjId, userIds, certId);
        } catch (Exception e) {
            log.error(
                "LOG42246:为员工颁发证书时发生错误: authprjId={}, userIdsCount={}, error={}", authprjId, userIds.size(), e.getMessage(),
                e);
        }
        log.info("为项目下所有员工同步颁发证书完成: authprjId={}", authprjId);
    }

    /**
     * 发送证书颁发MQ消息
     * @param sourceId 这里的sourceId必须是aom项目id，不能是authPrjId，否则证书那边跳转来源的时候会出错
     */
    private void sendCertIssueMessage(String orgId, String sourceId, String certTempId, List<String> userIds) {
        try {
            // 批量发送证书颁发消息
            CertIssueMessage message = new CertIssueMessage();
            message.setBusinessType(1300);
            message.setBatchId(ApiUtil.getUuid());
            message.setOrgId(orgId);
            message.setLogTime(new Date());
            message.setUserId(decideOperator());

            CerIssue4Post msgBody = new CerIssue4Post();
            msgBody.setOrgId(orgId);
            msgBody.setSpecialOrgId(orgId);
            msgBody.setUserIds(userIds); // 设置用户ID列表
            msgBody.setCerId(certTempId);
            msgBody.setSourceId(sourceId);
            msgBody.setSourceType(AppConstants.AUTHPRJ_CERT_SOURCE_FLAG);

            AuthprjPO authPrj = authprjMapper.selectByAomPrjId(orgId, sourceId);
            Validate.isNotNull(authPrj, AUTHPRJ_NOT_EXIST);
            msgBody.setSourceName(authPrj.getAomPrjName()); // 设置来源名称
            msgBody.setNotice(1); // 默认开启颁发通知

            message.setMsgBody(msgBody);

            String messageJson = JSON.toJSONString(message);
            rocketMqAclSender.asyncSend(TOPIC_CER_TEMP_ISSUE_V2, messageJson);

            log.info("发送证书颁发MQ消息成功: userIds={}, certTempId={}", userIds, certTempId);
        } catch (Exception e) {
            log.error("发送证书颁发MQ消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("证书颁发失败", e);
        }
    }

    private static String decideOperator() {
        try {
            return YxtBasicUtils.userInfo().getUserId();
        } catch(Exception e) {
            // 忽略，异步中运行时，有可能拿不到数据，此时会报错：
            // java.lang.IllegalStateException: The request object has been recycled and is no longer associated with this facade
        }
        return "system";
    }

    /**
     * 调用证书服务吊销证书
     */
    private void revokeCertificates(String orgId, String certTempId, List<String> issueIds, String operator) {
        CerRevoke4ExternalReq req = new CerRevoke4ExternalReq();
        req.setOptUserId(operator);
        req.setOrgId(orgId);
        req.setCerId(certTempId);
        req.setCerIssueIds(issueIds);

        certAclService.tempRevoke(req);
        log.info("调用证书服务吊销证书成功: orgId={}, certTempId={}, issueIds={}", orgId, certTempId, issueIds);
    }

    /**
     * 从证书服务获取颁发记录列表
     */
    private PagingList<AuthPrjCertIssueVO> getCertIssueListFromService(
        String orgId, String authprjId, String certTempId, AuthPrjCertIssue4Query query) {

        IssueListSearchConditionReq req = new IssueListSearchConditionReq();
        req.setOrgId(orgId);
        req.setCerId(certTempId);
        AuthprjPO authprjPO = authprjMapper.selectByOrgIdAndId(orgId, authprjId);
        Validate.isNotNull(authprjPO, AUTHPRJ_NOT_EXIST);
        req.setSourceId(authprjPO.getAomPrjId());
        req.setSourceType(AppConstants.AUTHPRJ_CERT_SOURCE_FLAG); // 1-项目发证

        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        req.setCurrent(Math.toIntExact(pageRequest.getCurrent()));
        req.setLimit(Math.toIntExact(pageRequest.getSize()));

        // 模糊搜索关键词内容(姓名、账号、证书编号)
        if (StringUtils.isNotBlank(query.getSearchKey())) {
            req.setKeyword(query.getSearchKey());
        }

        // 设置状态筛选
        if (query.getStatus() != null && query.getStatus() >= 0) {
            req.setStatusList(singletonList(query.getStatus()));
        }

        PagingList<IssueListSearchConditionResult> result = certAclService.getIssueList(req);

        if (result == null || CollectionUtils.isEmpty(result.getDatas())) {
            return CommonUtil.emptyPagingList();
        }

        List<AuthPrjCertIssueVO> voList =
            result.getDatas().stream().map(this::convertToAuthPrjCertIssueVO).collect(Collectors.toList());
        return new PagingList<>(voList, result.getPaging());
    }

    /**
     * 转换证书颁发记录对象
     */
    private AuthPrjCertIssueVO convertToAuthPrjCertIssueVO(IssueListSearchConditionResult source) {
        AuthPrjCertIssueVO target = new AuthPrjCertIssueVO();
        target.setIssueId(source.getCerIssueId());
        target.setIssueNo(source.getCerIssueNo());
        target.setUserId(source.getUserId());
        target.setFullName(source.getCnName());
        target.setUserName(source.getUserName());
        target.setDeptName(source.getDeptName());
        target.setPositionName(source.getPositionName());

        // 转换时间格式
        if (source.getIssueTime() != null) {
            target.setIssueTime(
                source.getIssueTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (source.getExpiredTime() != null) {
            target.setExpiredTime(
                source.getExpiredTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }

        target.setStatus(source.getStatus());
        return target;
    }

    /**
     * 批量检查是否已经颁发过证书
     */
    private Map<String, Boolean> batchHasIssuedCert(String orgId, String authprjId, String certTempId, List<String> userIds) {
        Map<String, Boolean> result = new HashMap<>();
        
        // 如果用户列表为空，直接返回空结果
        if (CollectionUtils.isEmpty(userIds)) {
            return result;
        }
        
        try {
            // 使用证书服务查询用户证书状态，确保数据一致性
            IssueListSearchConditionReq req = new IssueListSearchConditionReq();
            req.setOrgId(orgId);
            req.setCerId(certTempId);
            AuthprjPO authprjPO = authprjMapper.selectByOrgIdAndId(orgId, authprjId);
            Validate.isNotNull(authprjPO, AUTHPRJ_NOT_EXIST);
            req.setSourceId(authprjPO.getAomPrjId());
            req.setSourceType(AppConstants.AUTHPRJ_CERT_SOURCE_FLAG); // 1-项目发证
            req.setUserIds(userIds);
            req.setCurrent(1);
            req.setLimit(Integer.MAX_VALUE);

            // 只查询待颁发或已颁发状态的证书
            req.setStatusList(List.of(0));

            PagingList<IssueListSearchConditionResult> certResult = certAclService.getIssueList(req);
            
            // 初始化所有用户为未颁发状态
            for (String userId : userIds) {
                result.put(userId, false);
            }
            
            // 如果证书服务返回了待颁发或已颁发的证书记录，标记为已颁发
            if (certResult != null && !CollectionUtils.isEmpty(certResult.getDatas())) {
                for (IssueListSearchConditionResult cert : certResult.getDatas()) {
                    if (cert.getUserId() != null && cert.getStatus() == 0) {
                        result.put(cert.getUserId(), true);
                    }
                }
            }
        } catch (Exception e) {
            log.error("批量检查证书颁发状态失败: orgId={}, authprjId={}, certTempId={}, userIds={}, error={}", 
                orgId, authprjId, certTempId, userIds, e.getMessage(), e);
            // 出现异常时，所有用户都标记为未颁发
            for (String userId : userIds) {
                result.put(userId, false);
            }
        }
        
        return result;
    }

    /**
     * 检查用户是否满足证书获取条件
     */
    private boolean checkUserMeetsCertCondition(String orgId, AuthprjPO authprjPO, String userId, AuthPrjCertVO cert) {
        Integer obtainType = cert.getObtainType();
        String obtainValue = cert.getObtainValue();

        return switch (obtainType) {
            case 0 -> // 认证结果
                checkUserCertificationResult(orgId, authprjPO, userId, obtainValue);
            case 1 -> // 总得分大于
                checkUserTotalScore(orgId, authprjPO, userId, obtainValue);
//            case 2 -> // 通过率大于
//                checkUserPassRate(orgId, authprjPO, userId, obtainValue);
            case 3 -> // 通过特定认证任务
                checkUserSpecificTasks(orgId, authprjPO, userId, obtainValue);
            case 4 -> // 通过所有认证任务
                checkUserAllTasks(orgId, authprjPO, userId);
            default -> false;
        };
    }

    /**
     * 检查用户认证结果是否满足条件
     */
    private boolean checkUserCertificationResult(String orgId, AuthprjPO authprjPO, String userId, String obtainValue) {
        try {
            // 获取用户的认证结果
            AuthprjResultUserPO
                userResult = authprjResultUserMapper.selectByAuthprjIdAndUserId(orgId, authprjPO.getId(), userId);
            if (userResult == null || StringUtils.isBlank(userResult.getLevelId())) {
                return false;
            }

            // 获取分层规则信息
            AuthprjRuleLevelPO levelRule = authprjRuleLevelMapper.selectById(userResult.getLevelId());
            if (levelRule == null || levelRule.getDeleted() == 1) {
                return false;
            }

            // 检查分层结果是否在允许的范围内
            String[] allowedLevels = obtainValue.split(",");
            for (String allowedLevel : allowedLevels) {
                if (allowedLevel.trim().equals(levelRule.getId())) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("检查用户认证结果失败: orgId={}, authprjId={}, userId={}, error={}",
                orgId, authprjPO.getId(), userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查用户总得分是否满足条件
     */
    private boolean checkUserTotalScore(String orgId, AuthprjPO authprjPO, String userId, String obtainValue) {
        try {
            // 获取用户的认证结果
            AuthprjResultUserPO userResult = authprjResultUserMapper.selectByAuthprjIdAndUserId(orgId, authprjPO.getId(), userId);
            if (userResult == null || userResult.getScoreValue() == null) {
                return false;
            }

            // 解析阈值分数
            BigDecimal thresholdScore = new BigDecimal(obtainValue);

            // 比较用户得分是否大于等于阈值
            return userResult.getScoreValue().compareTo(thresholdScore) >= 0;
        } catch (Exception e) {
            log.error("检查用户总得分失败: orgId={}, authprjId={}, userId={}, error={}",
                orgId, authprjPO.getId(), userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查用户通过率是否满足条件
     */
    private boolean checkUserPassRate(String orgId, AuthprjPO authprjPO, String userId, String obtainValue) {
        try {
            // 获取认证项目下的所有活动
            AuthPrjRuleAppService authPrjRuleAppService = SpringContextHolder.getBean(AuthPrjRuleAppService.class);
            List<RuleOptionBean> actvItems = authPrjRuleAppService.getActivitiesByType(
                orgId, authprjPO.getId(),
                Arrays.asList(UacdTypeEnum.ACTV_EXAM.getRegId(), UacdTypeEnum.ACTV_JD.getRegId()));
            if (CollectionUtils.isEmpty(actvItems)) {
                return false;
            }

            int totalActivities = actvItems.size();
            int passedActivities = 0;

            // 检查每个活动的完成情况
            for (RuleOptionBean actvItem : actvItems) {
                if (isActvItemPassed(orgId, authprjPO, actvItem.getId(), userId)) {
                    passedActivities++;
                }
            }

            // 计算通过率
            BigDecimal passRate = BigDecimal.valueOf(passedActivities)
                .divide(BigDecimal.valueOf(totalActivities), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));

            // 解析阈值通过率
            BigDecimal thresholdRate = new BigDecimal(obtainValue);

            // 比较通过率是否大于等于阈值
            return passRate.compareTo(thresholdRate) >= 0;
        } catch (Exception e) {
            log.error("检查用户通过率失败: orgId={}, authprjId={}, userId={}, error={}",
                orgId, authprjPO.getId(), userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查用户是否完成特定任务
     */
    private boolean checkUserSpecificTasks(String orgId, AuthprjPO authprjPO, String userId, String obtainValue) {
        try {
            // 解析活动ID列表(指向rv_activity_arrange_item.ref_id)
            String[] refActvIds = obtainValue.split(",");

            // 检查每个指定活动是否都已完成
            for (String refActvId : refActvIds) {
                if (StringUtils.isNotBlank(refActvId.trim())) {
                    if (!isActvItemPassed(orgId, authprjPO, refActvId.trim(), userId)) {
                        log.info(
                            "用户未完成指定任务: orgId={}, authprjId={}, userId={}, refActvId={}",
                            orgId, authprjPO.getId(), userId, refActvId);
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("检查用户特定任务完成情况失败: orgId={}, authprjId={}, userId={}, error={}",
                orgId, authprjPO.getId(), userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查用户是否完成所有任务
     */
    private boolean checkUserAllTasks(String orgId, AuthprjPO authprjPO, String userId) {
        try {
            // 获取认证项目下的所有活动
            AuthPrjRuleAppService authPrjRuleAppService = SpringContextHolder.getBean(AuthPrjRuleAppService.class);
            List<RuleOptionBean> allActivities = authPrjRuleAppService.getActivitiesByType(
                orgId, authprjPO.getId(),
                Arrays.asList(UacdTypeEnum.ACTV_EXAM.getRegId(), UacdTypeEnum.ACTV_JD.getRegId()));
            if (CollectionUtils.isEmpty(allActivities)) {
                log.warn("LOG10027:项目没有设置任何鉴定/考试活动，认为不通过");
                return false;
            }

            // 检查每个鉴定活动是否都已通过
            for (RuleOptionBean activity : allActivities) {
                if (!isActvItemPassed(orgId, authprjPO, activity.getId(), userId)) {
                    log.info("用户未完成所有任务: orgId={}, authprjId={}, userId={}", orgId, authprjPO.getId(), userId);
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("检查用户所有任务完成情况失败: orgId={}, authprjId={}, userId={}, error={}",
                orgId, authprjPO, userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查用户是否通过指定活动
     */
    private boolean isActvItemPassed(String orgId, AuthprjPO authprjPO, String actvRefId, String userId) {
        try {
            // 1. 检查考试/测评/鉴定活动结果
            RvAssessmentActivityResultPO assessmentResult = rvAssessmentActivityResultMapper
                .selectByActvRefIdAndUserId(orgId, authprjPO.getAomPrjId(), actvRefId, userId);
            if (assessmentResult != null) {
                // 检查是否通过（passed=1表示通过）
                return assessmentResult.getPassed() != null && assessmentResult.getPassed() == 1;
            }
            log.info("没有结果，认为没通过");
            return false;
        } catch (Exception e) {
            log.error(
                "检查活动通过状态失败: orgId={}, actvRefId={}, userId={}, error={}",
                orgId, actvRefId, userId, e.getMessage(), e);
            return false;
        }
    }

}
