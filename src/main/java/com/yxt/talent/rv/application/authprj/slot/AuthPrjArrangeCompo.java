package com.yxt.talent.rv.application.authprj.slot;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.base.bean.common.Activity4Create;
import com.yxt.aom.base.bean.common.Activity4Get;
import com.yxt.aom.base.bean.common.Activity4List;
import com.yxt.aom.base.bean.common.Activity4Update;
import com.yxt.aom.base.custom.CustomArrangeCompo;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.common.exception.ApiException;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.enums.DeleteEnum;
import com.yxt.talent.rv.application.authprj.AuthPrjRuleAppService;
import com.yxt.talent.rv.application.authprj.AuthPrjTodoAndMsgService;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeForAuthPrjEnum;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 认证项目自定义接口
 */
@Slf4j
@RequiredArgsConstructor
@Component("customArrangeCompo4ProjAuthpj")
public class AuthPrjArrangeCompo implements CustomArrangeCompo {

    private final AuthprjMapper authprjMapper;
    private final ActivityService activityService;
    private final AuthPrjRuleAppService authPrjRuleAppService;
    private final AuthService authService;
    private final AuthPrjTodoAndMsgService authPrjTodoService;

    @Override
    public void checkBeforeCreateActivity(String orgId, String userId, Activity4Create bean) {
    }

    @Override
    public void invokeAfterCreateActivity(String orgId, String userId, Activity4Create bean) {
        log.info("创建认证项目成功，orgId={},userId={},bean:{}", orgId, userId, BeanHelper.bean2Json(
            bean,
            JsonInclude.Include.NON_NULL));
        //创建认证项目项目
        String modelId = bean.getModelId();
        String prjId = bean.getId();
        AuthprjPO authprjPO = new AuthprjPO();
        authprjPO.setModelId(modelId);
        authprjPO.setAomPrjId(prjId);
        authprjPO.setOrgId(orgId);
        authprjPO.setId(ApiUtil.getUuid());
        authprjPO.setCreateTime(LocalDateTime.now());
        authprjPO.setUpdateTime(LocalDateTime.now());
        authprjPO.setCreateUserId(userId);
        authprjPO.setUpdateUserId(userId);
        authprjPO.setDeleted(DeleteEnum.NOT_DELETED.getCode());
        authprjMapper.insert(authprjPO);

        authPrjTodoService.createEmptyTodo(orgId, userId, authprjPO.getId());
    }

    @Override
    public void checkBeforeUpdateActivity(String orgId, String userId, Activity4Update bean) {
        Activity activity = activityService.findById(orgId, bean.getId());
        if (activity == null) {
            throw new ApiException("apis.aom.common.id.invalid", new Serializable[0]);
        }
        ActivityStatus activityStatus = getActivityStatus(activity);
        if (!activityStatus.isCanEdit()) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_STATUS_CAN_NOT_EDIT);
        }

        if (bean.getTimeModel() != null && !bean.getTimeModel().equals(activity.getTimeModel())) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_TIMEMODE_CAN_NOT_EDIT);
        }

        if (activity.getActvStatus() >= 2) {
            boolean canEdit = true;
            if (bean.getModelId() != null && !bean.getModelId().equals(activity.getModelId())) {
                canEdit = false;
            }
            if (bean.getTimeModel() != null && !bean.getTimeModel().equals(activity.getTimeModel())) {
                canEdit = false;
            }
            if (!canEdit) {
                throw new ApiException(ExceptionKeys.AUTHPRJ_STATUS_CAN_NOT_EDIT_FOR_DOING);
            }
        }
    }

    @Override
    public void invokeAfterUpdateActivity(String orgId, String userId, Activity4Update bean) {
        Activity activity = activityService.findById(orgId, bean.getId());
        if (activity == null) {
            throw new ApiException("apis.aom.common.id.invalid", new Serializable[0]);
        }
        //更新项目模型
        AuthprjPO authprjPO = authprjMapper.selectByAomPrjId(orgId, bean.getId());
        if (authprjPO == null) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_NOT_EXIST);
        }
        String oldModelId = authprjPO.getModelId();
        authprjPO.setModelId(bean.getModelId());
        authprjPO.setUpdateTime(LocalDateTime.now());
        authprjPO.setUpdateUserId(userId);
        authprjMapper.updateById(authprjPO);

        boolean isEditModel = bean.getModelId() != null && !bean.getModelId().equals(oldModelId);
        if (isEditModel){
            //  清空活动, 清空规则配置
            activityService.sendClearItemsMq(orgId, bean.getId(), UacdTypeForAuthPrjEnum.PRJ_AUTH.getRegId(), userId, authService.getLocale().getLanguage());
            authPrjRuleAppService.clear(orgId, authprjPO.getId(), userId);
        }
    }

    @Override
    public Activity4Get getActivity4Mgr(Activity4Get bean) {
        return bean;
    }

    @Override
    public Activity4Get getActivity4Stu(Activity4Get bean) {
        return bean;
    }

    @Override
    public List<Activity4List> searchActivity(List<Activity4List> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        return list;
    }

    @Override
    public void invokeAfterActvDraftReleased(String orgId, String userId, String actvId) {
        log.debug("LOG67960:orgId={},userId={},actvId:{}", orgId, userId, actvId);
    }

    /**
     * 获取认证项目状态
     */
    private ActivityStatus getActivityStatus(Activity activity) {
        ActivityStatus activityStatus = new ActivityStatus();
        // 状态(0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回)
        if (activity.getActvStatus() < 2) {
            activityStatus.setCanEdit(true);
            activityStatus.setCanPublish(true);
            activityStatus.setCanDelete(true);
        }
        if (activity.getActvStatus() == 2) {
            activityStatus.setCanEdit(true);
            activityStatus.setCanFinish(true);
        }
        if (activity.getActvStatus() == 3) {
            activityStatus.setCanDelete(true);
        }
        return activityStatus;
    }

    @Getter
    @Setter
    static class ActivityStatus {
        boolean canEdit = false;
        boolean canPublish = false;
        boolean canDelete = false;
        boolean canFinish = false;
    }


}
