package com.yxt.talent.rv.application.authprj;

import com.yxt.cerapifacade.bean.CerReqBean;
import com.yxt.cerapifacade.bean.CerSimpleInfoBean;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.Validate;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.rv.controller.manage.authprj.cmd.AuthPrjCert4Create;
import com.yxt.talent.rv.controller.manage.authprj.cmd.AuthPrjCert4Update;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertRemindVO;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertVO;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjCertMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjCertObtainItemMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjCertRemindMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjCertObtainItemPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjCertPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjCertRemindPO;
import com.yxt.talent.rv.infrastructure.service.remote.CertAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.AUTHPRJ_CERT_NOT_EXIST;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.ORG_ID_NOT_MATCH;

/**
 * 认证项目证书基础管理服务
 * 负责证书的基础CRUD操作、配置管理、信息补充
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthPrjCertAppService {

    private final AuthprjCertMapper authprjCertMapper;
    private final AuthprjCertRemindMapper authprjCertRemindMapper;
    private final AuthprjCertObtainItemMapper authprjCertObtainItemMapper;
    private final CertAclService certAclService;
    private final AuthPrjCertIssueService authPrjCertIssueService;

    /**
     * 获取认证项目证书列表
     */
    public List<AuthPrjCertVO> list(String orgId, String authprjId) {
        List<AuthPrjCertVO> certList = authprjCertMapper.selectCertListByAuthprjId(orgId, authprjId);

        if (CollectionUtils.isNotEmpty(certList)) {
            // 补充证书提醒信息
            enrichCertRemindInfo(orgId, authprjId, certList);
            // 补充证书获取项信息
            enrichCertObtainItemInfo(orgId, certList);
            // 补充证书模板信息
            enrichCertTemplateInfo(orgId, certList);
        }

        return certList;
    }

    /**
     * 获取证书详情
     */
    public AuthPrjCertVO details(String orgId, String authprjId, String certId) {
        AuthprjCertPO authprjCertPO = authprjCertMapper.selectById(certId);
        Validate.isNotNull(authprjCertPO, AUTHPRJ_CERT_NOT_EXIST);

        AuthPrjCertVO authPrjCertVO = new AuthPrjCertVO();
        BeanCopierUtil.copy(authprjCertPO, authPrjCertVO);

        // 补充证书提醒信息
        enrichCertRemindInfo(orgId, authprjId, Collections.singletonList(authPrjCertVO));
        // 补充证书获取项信息
        enrichCertObtainItemInfo(orgId, Collections.singletonList(authPrjCertVO));
        // 补充证书模板信息
        enrichCertTemplateInfo(orgId, Collections.singletonList(authPrjCertVO));

        return authPrjCertVO;
    }

    /**
     * 添加证书
     */
    public void add(String orgId, String authprjId, AuthPrjCert4Create command) {
        String operator = YxtBasicUtils.userInfo().getUserId();

        // 1. 创建证书记录
        AuthprjCertPO certPO = new AuthprjCertPO();
        BeanUtils.copyProperties(command, certPO);
        certPO.setId(ApiUtil.getUuid());
        certPO.setOrgId(orgId);
        certPO.setAuthprjId(authprjId);
        certPO.setDeleted(0);
        EntityUtil.setAuditFields(certPO, operator);

        authprjCertMapper.insert(certPO);

        // 2. 保存到期提醒配置
        saveRemindConfigs(certPO.getId(), orgId, command.getReminds(), operator);

        // 3. 保存获取条件配置
        saveObtainItems(
            certPO.getId(), orgId, authprjId, command.getCertTempId(), command.getObtainType(),
            command.getObtainItems(), operator);

        // 4. 验证并颁发证书
        authPrjCertIssueService.issueCerts(orgId, authprjId, certPO.getId());
    }

    /**
     * 编辑证书
     */
    public void modify(String orgId, String authprjId, String certId, AuthPrjCert4Update command) {
        String operator = YxtBasicUtils.userInfo().getUserId();

        // 1. 验证证书存在
        AuthprjCertPO existingCert = authprjCertMapper.selectById(certId);
        Validate.isNotNull(existingCert, AUTHPRJ_CERT_NOT_EXIST);
        Validate.isTrue(
            orgId.equals(existingCert.getOrgId()) && authprjId.equals(existingCert.getAuthprjId()),
            ORG_ID_NOT_MATCH);

        // 2. 更新证书记录
        existingCert.setObtainType(command.getObtainType());
        existingCert.setObtainValue(command.getObtainValue());
        EntityUtil.setUpdate(existingCert, operator);

        authprjCertMapper.updateById(existingCert);

        // 3. 删除旧的提醒配置
        authprjCertRemindMapper.deleteByAuthprjCertId(orgId, certId, operator);

        // 4. 保存新的提醒配置
        saveRemindConfigs(certId, orgId, command.getReminds(), operator);

        // 5. 删除旧的获取条件配置
        authprjCertObtainItemMapper.deleteByAuthprjCertId(orgId, certId, operator);

        // 6. 保存新的获取条件配置
        saveObtainItems(
            certId, orgId, authprjId, existingCert.getCertTempId(), command.getObtainType(),
            command.getObtainItems(), operator);

        // 7. 验证并颁发证书
        authPrjCertIssueService.issueCerts(orgId, authprjId, existingCert.getId());
    }

    /**
     * 删除证书
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String orgId, String authprjId, String certId) {
        String operator = YxtBasicUtils.userInfo().getUserId();

        // 1. 验证证书存在
        AuthprjCertPO existingCert = authprjCertMapper.selectById(certId);
        Validate.isNotNull(existingCert, AUTHPRJ_CERT_NOT_EXIST);
        Validate.isTrue(
            orgId.equals(existingCert.getOrgId()) && authprjId.equals(existingCert.getAuthprjId()),
            ORG_ID_NOT_MATCH);

        // 2. 逻辑删除证书记录
        existingCert.setDeleted(1);
        EntityUtil.setUpdate(existingCert, operator);
        authprjCertMapper.updateById(existingCert);

        // 3. 删除相关配置
        authprjCertRemindMapper.deleteByAuthprjCertId(orgId, certId, operator);
        authprjCertObtainItemMapper.deleteByAuthprjCertId(orgId, certId, operator);
    }

    /**
     * 补充证书获取项信息
     */
    public void enrichCertObtainItemInfo(String orgId, List<AuthPrjCertVO> certList) {
        // 如果obtainType = 3 时，需要填充obtainItems
        certList.forEach(cert -> {
            if (cert.getObtainType() == 3) {
                List<String> obtainItems = authprjCertObtainItemMapper.selectObtainItemsByCertId(orgId, cert.getId());
                cert.setObtainItems(obtainItems);
            }
        });
    }

    /**
     * 补充证书提醒信息
     */
    public void enrichCertRemindInfo(String orgId, String authprjId, List<AuthPrjCertVO> certList) {
        // 全量查出项目下的所有证书的提醒配置，然后对号入座，每个证书可能会有多个提醒配置
        List<AuthPrjCertRemindVO> remindList = authprjCertRemindMapper.selectRemindListByAuthprjId(orgId, authprjId);

        for (AuthPrjCertVO cert : certList) {
            List<AuthPrjCertRemindVO> certRemindList = remindList.stream()
                .filter(remind -> Objects.equals(remind.getAuthprjCertId(), cert.getId()))
                .collect(Collectors.toList());
            cert.setReminds(certRemindList);
        }
    }

    /**
     * 补充证书模板信息
     */
    public void enrichCertTemplateInfo(String orgId, List<AuthPrjCertVO> certList) {
        try {
            // 收集所有证书模板ID
            List<String> certTempIds = certList.stream()
                .map(AuthPrjCertVO::getCertTempId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(certTempIds)) {
                return;
            }

            // 调用证书服务获取证书模板信息
            CerReqBean cerReqBean = new CerReqBean();
            cerReqBean.setOrgId(orgId);
            cerReqBean.setIds(certTempIds);

            List<CerSimpleInfoBean> certTemplates = certAclService.getCerList(cerReqBean);

            if (CollectionUtils.isNotEmpty(certTemplates)) {
                // 构建证书模板信息映射
                java.util.Map<String, CerSimpleInfoBean> certTemplateMap = certTemplates.stream()
                    .collect(
                        Collectors.toMap(CerSimpleInfoBean::getId, cert -> cert, (existing, replacement) -> existing));

                // 补充证书模板信息
                certList.forEach(cert -> {
                    CerSimpleInfoBean template = certTemplateMap.get(cert.getCertTempId());
                    if (template != null) {
                        if (template.getDeleted() == 1) {
                            log.warn("LOG10186: 证书模板已删除: certTempId={}", cert.getCertTempId());
                        }
                        if (template.getDisabled() == 1) {
                            log.warn("LOG10196: 证书模板已禁用: certTempId={}", cert.getCertTempId());
                        }
                        if (template.getExpired() == 1) {
                            log.warn("LOG10206: 证书模板已过期: certTempId={}", cert.getCertTempId());
                        }
                        cert.setCertTempName(template.getName());
                        cert.setCertCoverUrl(template.getCoverUrl());
                        cert.setValidMonth(template.getValidMonth());
                        cert.setDisabled(template.getDisabled());
                        cert.setExpired(template.getExpired());

                        // 如果证书从“有期限”变为“永久有效”，则删除到期提醒
                        if (template.getValidMonth() == 0 && CollectionUtils.isNotEmpty(cert.getReminds())) {
                            log.info("LOG41136:证书(certId={})已变为永久有效，删除到期提醒配置", cert.getId());
                            authprjCertRemindMapper.deleteByAuthprjCertId(orgId, cert.getId(), "system");
                            cert.setReminds(new ArrayList<>()); // 清空VO中的提醒列表，确保返回数据一致
                        }
                    }
                });
            }
        } catch (Exception e) {
            log.error("补充证书模板信息失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 保存到期提醒配置
     */
    private void saveRemindConfigs(String certId, String orgId, List<AuthPrjCertRemindVO> reminds, String operator) {
        if (CollectionUtils.isEmpty(reminds)) {
            return;
        }

        List<AuthprjCertRemindPO> remindPOList = reminds.stream().map(remind -> {
            AuthprjCertRemindPO remindPO = new AuthprjCertRemindPO();
            BeanUtils.copyProperties(remind, remindPO);
            remindPO.setId(ApiUtil.getUuid());
            remindPO.setOrgId(orgId);
            remindPO.setAuthprjCertId(certId);
            remindPO.setDeleted(0);
            EntityUtil.setAuditFields(remindPO, operator);
            return remindPO;
        }).collect(Collectors.toList());

        authprjCertRemindMapper.batchInsertOrUpdate(remindPOList);
    }

    /**
     * 保存获取条件配置
     */
    private void saveObtainItems(
        String certId, String orgId, String authprjId, String certTempId, Integer obtainType,
        List<String> obtainItems, String operator) {
        // 只有当获取方式为"通过特定认证任务"时才需要保存活动ID
        if (obtainType != null && obtainType == 3 && CollectionUtils.isNotEmpty(obtainItems)) {
            List<AuthprjCertObtainItemPO> obtainItemPOList = new ArrayList<>();

            for (String actId : obtainItems) {
                if (StringUtils.isNotBlank(actId.trim())) {
                    AuthprjCertObtainItemPO obtainItemPO = new AuthprjCertObtainItemPO();
                    obtainItemPO.setId(ApiUtil.getUuid());
                    obtainItemPO.setOrgId(orgId);
                    obtainItemPO.setAuthprjCertId(certId);
                    obtainItemPO.setAuthprjId(authprjId);
                    obtainItemPO.setCertTempId(certTempId);
                    obtainItemPO.setActId(actId.trim());
                    obtainItemPO.setDeleted(0);
                    EntityUtil.setAuditFields(obtainItemPO, operator);
                    obtainItemPOList.add(obtainItemPO);
                }
            }

            if (!obtainItemPOList.isEmpty()) {
                authprjCertObtainItemMapper.batchInsertOrUpdate(obtainItemPOList);
            }
        }
    }
}
