package com.yxt.talent.rv.application.activity;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.activity.service.rollup.impl.ResultRollUpService;
import com.yxt.aom.base.bean.arrange.ActivityArrangeItem4ActvMgr;
import com.yxt.aom.base.bean.arrange.ActivityArrangeTree;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.service.arrange.ArrangeService;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.datamodel.activityresult.AssessmentActivityResult;
import com.yxt.aom.datamodel.activityresult.IndexResult;
import com.yxt.aom.datamodel.common.ActionEnum;
import com.yxt.aom.datamodel.common.Actor;
import com.yxt.aom.datamodel.common.TargetObject;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.util.*;
import com.yxt.enums.DeleteEnum;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.spsdfacade.bean.spsd.ModelInfo;
import com.yxt.spsdfacade.bean.spsd.ModelRequireBaseInfo;
import com.yxt.spsdfacade.bean.spsd.ModelRequireDetailInfo;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.spsdk.common.bean.RuleMainBase;
import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.talent.rv.application.activity.dto.*;
import com.yxt.talent.rv.application.xpd.common.dto.AomActvExtDto;
import com.yxt.talent.rv.application.xpd.common.dto.AomActvIndicatorDto;
import com.yxt.talent.rv.application.xpd.common.dto.PerfExtDto;
import com.yxt.talent.rv.application.xpd.common.dto.PerfResultDto;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfPeriodVO;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.CalcLogTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.PerfEvalTimeTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.enums.PerfEvalTypeEnum;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfResultConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdCalcLogPO;
import com.yxt.talent.rv.infrastructure.repository.aom.RvActivityParticipationMemberRepo;
import com.yxt.talent.rv.infrastructure.repository.xpd.XpdCalcBatchDTO;
import com.yxt.talent.rv.infrastructure.repository.xpd.XpdCalcLogRepo;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_PERF_ACTIVITY_CALC;

/**
 * 绩效活动服务
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PerfActivityService {

    private final ActivityPerfMapper activityPerfMapper;
    private final PerfPeriodMapper perfPeriodMapper;
    private final ActivityPerfConfMapper activityPerfConfMapper;
    private final ActivityPerfResultConfMapper activityPerfResultConfMapper;
    private final PerfMapper perfMapper;
    private final ActivityPerfResultMapper activityPerfResultMapper;
    private final RvAomActivityService aomActivityService;
    private final PerfGradeMapper perfGradeMapper;
    private final ArrangeService arrangeService;
    private final AuthService authService;
    private final RvActivityParticipationMemberRepo rvActivityParticipationMemberRepo;
    private final ResultRollUpService resultRollUpService;
    private final ActivityService activityService;
    private final SpRuleService spRuleService;
    private final XpdCalcLogRepo xpdCalcLogRepo;
    private final ILock lockService;
    private final SptalentsdFacade spTalentSdFacade;
    private final XpdService xpdService;
    private final RocketMqAclSender rocketMqAclSender;
    private final ActivityCalcComponent activityCalcComponent;

    /**
     * 查询绩效周期列表
     */
    public List<PerfPeriodVO> queryPeriods(String orgId) {
        List<PerfPeriodVO> result = new ArrayList<>();
        List<PerfPeriodPO> list = perfPeriodMapper.selectByOrgId(orgId);
        if (CollectionUtils.isNotEmpty(list)) {
            result = BeanCopierUtil.convertList(list, PerfPeriodPO.class, PerfPeriodVO.class);
        }
        return result;
    }

    /**
     * 创建绩效活动
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public ActivityPerfPO createPerfActivity(String orgId, String opUserId, PerfActivityDTO perfActivityDTO) {
        // 校验绩效活动信息是否合法
        validateActivity(orgId, perfActivityDTO);

        // 创建绩效活动基础信息
        ActivityPerfPO activityPerfPO = new ActivityPerfPO();
        BeanHelper.copyProperties(perfActivityDTO, activityPerfPO);
        activityPerfPO.setActvName(perfActivityDTO.getName());
        activityPerfPO.setId(ApiUtil.getUuid());
        // 目前外部ID跟活动ID一致
        activityPerfPO.setAomActId(activityPerfPO.getId());
        activityPerfPO.setOrgId(orgId);
        activityPerfPO.setCreateTime(LocalDateTime.now());
        activityPerfPO.setUpdateTime(LocalDateTime.now());
        activityPerfPO.setCreateUserId(opUserId);
        activityPerfPO.setUpdateUserId(opUserId);
        activityPerfPO.setDeleted(DeleteEnum.NOT_DELETED.getCode());
        activityPerfMapper.insert(activityPerfPO);

        // 创建绩效得分权重，绩效评估结果配置
        addActivityConfList(orgId, opUserId, perfActivityDTO, activityPerfPO.getId());

        return activityPerfPO;
    }

    /**
     * 创建，编辑绩效活动时，推送ext参数给aom。前端主动获取
     *
     * @param orgId
     * @param perfActivityExtDTO
     * @return
     */
    public AomActvExtDto getPerfActivityExtInfo(String orgId, PerfActivityExtDTO perfActivityExtDTO) {
        AomActvExtDto aomActvExtDto = new AomActvExtDto();
        // 绩效基础信息
        PerfExtDto perfExtDto = new PerfExtDto();
        perfExtDto.setEvalType(perfActivityExtDTO.getEvalType());
        perfExtDto.setPeriodIds(perfActivityExtDTO.getPeriodIds());
        if (CollectionUtils.isNotEmpty(perfActivityExtDTO.getResultConfDTOS())) {
            perfExtDto.setPerfResultDtos(
                    BeanCopierUtil.convertList(perfActivityExtDTO.getResultConfDTOS(), PerfResultConfDTO.class,
                            PerfResultDto.class));
        }

        List<String> pids = Lists.newArrayList(perfActivityExtDTO.getPeriodIds().split(";"));
        List<PerfPeriodPO> periods = perfPeriodMapper.selectByOrgIdAndIds(orgId, pids);
        if (CollectionUtils.isNotEmpty(periods)) {
            perfExtDto.setPeriodNames(
                    String.join(";", periods.stream().map(PerfPeriodPO::getPeriodName).collect(Collectors.toList())));
        }

        aomActvExtDto.setPerfExtDto(perfExtDto);

        // 绩效指标信息
        List<AomActvIndicatorDto> aomActvIndicatorDtos = new ArrayList<>();
        AomActvIndicatorDto aomActvIndicatorDto = new AomActvIndicatorDto();
        aomActvIndicatorDto.setSdIndicatorId(perfActivityExtDTO.getIndicatorId());
        BigDecimal totalScore = BigDecimal.ZERO;
        if (perfActivityExtDTO.getEvalType() == PerfEvalTypeEnum.SCORE.getCode()) {
            List<PerfConfDTO> perfConfPOS = perfActivityExtDTO.getConfList();
            if (CollectionUtils.isEmpty(perfConfPOS)) {
                return aomActvExtDto;
            }
            Map<String, BigDecimal> weightMap = StreamUtil.list2map(perfConfPOS, PerfConfDTO::getPeriodId,
                    PerfConfDTO::getWeight);

            List<String> periodIds = Lists.newArrayList(perfActivityExtDTO.getPeriodIds().split(";"));
            if (CollectionUtils.isEmpty(periodIds)) {
                throw new ApiException(ExceptionKeys.PERF_ACTIVITY_PERIODID_NOT_NULL);
            }
            List<PerfPeriodPO> perfPOS = validatePeriod(orgId, periodIds);

            for (PerfPeriodPO perf : perfPOS) {
                if (weightMap.containsKey(perf.getId())) {
                    totalScore = totalScore.add(perf.getScoreTotal().multiply(
                            weightMap.get(perf.getId()).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)));
                }
            }
        }

        if (perfActivityExtDTO.getEvalType() == PerfEvalTypeEnum.LEVEL.getCode()) {
            List<PerfResultConfDTO> perfResultConfPOS = perfActivityExtDTO.getResultConfDTOS();
            if (CollectionUtils.isNotEmpty(perfResultConfPOS)) {
                totalScore = perfResultConfPOS.get(0).getScore();
            }
        }

        aomActvIndicatorDto.setTotalScore(totalScore);
        aomActvIndicatorDtos.add(aomActvIndicatorDto);

        aomActvExtDto.setIndicators(aomActvIndicatorDtos);


        return aomActvExtDto;
    }

    public List<PerfPeriodPO> validatePeriod(String orgId, List<String> periodIds) {
        List<PerfPeriodPO> perfPOS = perfPeriodMapper.selectByOrgIdAndIds(orgId, periodIds);
        if (CollectionUtils.isEmpty(perfPOS)) {
            throw new ApiException(ExceptionKeys.PERF_ACTIVITY_PERIODID_NOTEXIST);
        }
        List<PerfPeriodPO> noTotalScorePeriods = perfPOS.stream().filter(e -> e.getScoreTotal() == null)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noTotalScorePeriods)) {
            throw new ApiException(ExceptionKeys.PERF_ACTIVITY_PERIODID_TOTAL_SCORE_NOTEXIST);
        }
        return perfPOS;
    }

    public void validateActivity(String orgId, PerfActivityDTO perfActivityDTO) {
        if (perfActivityDTO.getEvalTimeType() == PerfEvalTimeTypeEnum.FIXEDTIME.getCode()
                && perfActivityDTO.getEvalTime() == null) {
            throw new ApiException("apis.sptalentrv.perf.activity.evaltime.notnull");
        }
        if (perfActivityDTO.getEvalType() == PerfEvalTypeEnum.SCORE.getCode() && CollectionUtils.isEmpty(
                perfActivityDTO.getConfList())) {
            throw new ApiException("apis.sptalentrv.perf.activity.conflist.notnull");
        }
        if (perfActivityDTO.getEvalType() == PerfEvalTypeEnum.LEVEL.getCode() && CollectionUtils.isEmpty(
                perfActivityDTO.getResultConfDTOS())) {
            throw new ApiException("apis.sptalentrv.perf.activity.resultconf.notnull");
        }
        if (perfActivityDTO.getEvalType() == PerfEvalTypeEnum.SCORE.getCode()) {

            if (perfActivityDTO.getScoreQualified() == null) {
                throw new ApiException("apis.sptalentrv.perf.activity.scorequalified.notnull");
            }

            // 评估方式为绩效得分时
            // 校验权重是否合法 校验权重是否为100
            BigDecimal totalWeight = BigDecimal.ZERO;
            List<PerfConfDTO> confList = perfActivityDTO.getConfList();

            if (confList != null && !confList.isEmpty()) {
                for (PerfConfDTO conf : confList) {
                    BigDecimal weight = conf.getWeight();
                    if (weight != null) {
                        totalWeight = totalWeight.add(weight);
                    }
                }
            }
            // 校验权重加起来是否等于100
            if (totalWeight.compareTo(BigDecimal.valueOf(100)) != 0) {
                throw new ApiException("apis.sptalentrv.perf.activity.weight.error");
            }
            List<String> periodIds = Lists.newArrayList(perfActivityDTO.getPeriodIds().split(";"));
            if (CollectionUtils.isEmpty(periodIds)) {
                throw new ApiException(ExceptionKeys.PERF_ACTIVITY_PERIODID_NOT_NULL);
            }
            validatePeriod(orgId, periodIds);
        }

    }

    public void validatePerfIndicator(String orgId, String prjId, String regId, String perfActId, String indicatorId) {
        // 校验指标是否存在
        ActivityArrangeTree<ActivityArrangeItem4ActvMgr> activityBean = arrangeService.listArrangeTree4ActvMgr(
                authService.getUserCacheDetail(), prjId, regId, false, false);
        List<String> perfActIds = new ArrayList<>();
        if (activityBean != null && CollectionUtils.isNotEmpty(activityBean.getDatas())) {
            if (StringUtils.isNotBlank(perfActId)) {
                // 走更新，排除这个活动。
            }
        }
        if (CollectionUtils.isEmpty(perfActIds)) {
            return;
        }
        List<ActivityPerfPO> perfActs = activityPerfMapper.selectByIds(orgId, perfActIds);
        if (CollectionUtils.isNotEmpty(perfActs)) {
            List<String> inds = perfActs.stream().filter(e -> StringUtils.isNotBlank(e.getIndicatorId()))
                    .map(ActivityPerfPO::getIndicatorId).collect(Collectors.toList());
            if (inds.contains(indicatorId)) {
                throw new ApiException(ExceptionKeys.PERF_INDICATOR_EXITE);
            }
        }

    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void updatePerfActivity(String orgId, String opUserId, PerfActivityDTO perfActivityDTO) {
        // 创建绩效活动基础信息
        if (StringUtils.isBlank(perfActivityDTO.getId())) {
            throw new ApiException("apis.sptalentrv.perf.activity.id.notnull");
        }

        ActivityPerfPO activityPerfPO = activityPerfMapper.selectById(orgId, perfActivityDTO.getId());
        if (activityPerfPO == null) {
            // 绩效活动不存在
            throw new ApiException("apis.sptalentrv.perf.activity.null");
        }

        Activity aomActv = activityService.findById(orgId, perfActivityDTO.getId());
        if (aomActv == null) {
            // 绩效活动不存在
            throw new ApiException("apis.sptalentrv.perf.activity.null");
        }

        // 校验绩效活动信息是否合法
        validateActivity(orgId, perfActivityDTO);

        if (aomActv.getActvStatus() < 2) {
            // 未发布，都可编辑
            log.info("未发布，都可编辑, {}", BeanHelper.bean2Json(perfActivityDTO, JsonInclude.Include.NON_NULL));
            BeanHelper.copyProperties(perfActivityDTO, activityPerfPO);
            activityPerfPO.setActvName(perfActivityDTO.getName());
            activityPerfPO.setOrgId(orgId);
            activityPerfPO.setUpdateTime(LocalDateTime.now());
            activityPerfPO.setUpdateUserId(opUserId);
            activityPerfMapper.insertOrUpdate(activityPerfPO);
        }

        if (aomActv.getActvStatus() == 2) {
            // 进行中 编辑任务名称、评估时间、任务说明
            log.info("进行中 编辑任务名称、评估时间、任务说明, {}",
                    BeanHelper.bean2Json(perfActivityDTO, JsonInclude.Include.NON_NULL));
            activityPerfPO.setActvName(perfActivityDTO.getName());
            activityPerfPO.setEvalTime(perfActivityDTO.getEvalTime());
            activityPerfPO.setEvalTimeType(perfActivityDTO.getEvalTimeType());
            activityPerfPO.setOrgId(orgId);
            activityPerfPO.setUpdateTime(LocalDateTime.now());
            activityPerfPO.setUpdateUserId(opUserId);
            activityPerfMapper.insertOrUpdate(activityPerfPO);
        }

        if (aomActv.getActvStatus() > 2) {
            // 结束，不能编辑
            log.info("结束，不能编辑, {}", BeanHelper.bean2Json(perfActivityDTO, JsonInclude.Include.NON_NULL));
            return;
        }

        if (aomActv.getActvStatus() < 2) {
            log.info("未发布，都可编辑, 配置可编辑");
            // 设置绩效得分权重，绩效评估结果配置 , 可以考虑真删。如果用不到以前的值
            deleteActivityConfList(orgId, perfActivityDTO.getId());
            // 创建绩效得分权重，绩效评估结果配置
            addActivityConfList(orgId, opUserId, perfActivityDTO, activityPerfPO.getId());
        }

    }

    public PerfActivityDTO getPerfActivityDetail(String orgId, String id) {
        ActivityPerfPO activityPerfPO = activityPerfMapper.selectById(orgId, id);
        PerfActivityDTO activityPerfDTO = new PerfActivityDTO();
        BeanHelper.copyProperties(activityPerfPO, activityPerfDTO);
        List<ActivityPerfConfPO> confs = activityPerfConfMapper.selectByActivityId(orgId, id);
        List<ActivityPerfResultConfPO> resultConfs = activityPerfResultConfMapper.selectByActivityId(orgId, id);
        activityPerfDTO.setConfList(BeanCopierUtil.convertList(confs, ActivityPerfConfPO.class, PerfConfDTO.class));
        activityPerfDTO.setResultConfDTOS(
                BeanCopierUtil.convertList(resultConfs, ActivityPerfResultConfPO.class, PerfResultConfDTO.class));


        //        Validate.isNotBlank(actProfileId, ExceptionKeys.PROFILE_PARAM_ID_EMPTY);
        //        ActivityProfilePO profilePO = activityProfileRepo.findById(orgId, actProfileId);
        //        Validate.isNotNull(profilePO, ExceptionKeys.PROFILE_NOT_FOUND);
        //        ProfileStatisticsVO vo = new ProfileStatisticsVO();
        //        vo.setEvalTimeType(profilePO.getEvalTimeType());
        //        if (profilePO.getEvalTimeType() == 2) {
        //            vo.setEvalTime(profilePO.getEvalTime());
        //        } else {
        //            获取最近的计算时间
        //            vo.setEvalTime(LocalDateTime.now());
        //        }
        //        List<ActivityProfileIndicatorPO> profileIndicatorPOList = activityProfileIndicatorRepo.findByActProfileId(orgId,
        //            actProfileId);
        //        List<String> indicatorStrList = new ArrayList<>();
        //        vo.setFinishedUserCount(0L)
        //        if (CollectionUtils.isNotEmpty(profileIndicatorPOList)) {
        //            List<String> indicatorIds = profileIndicatorPOList.stream()
        //                .map(ActivityProfileIndicatorPO::getSdIndicatorId).toList();
        //            String modelId = profilePO.getModelId();
        //            List<IndicatorDto> indicatorDtos = spsdAclService.getIndicatorByModelId(orgId, modelId);
        //            Map<String, String> indicatorIdNameMap = StreamUtil.list2map(indicatorDtos, IndicatorDto::getId,
        //                IndicatorDto::getIndicatorName);
        //            indicatorIds.forEach(indicatorId -> {
        //                if (indicatorIdNameMap.containsKey(indicatorId)) {
        //                    indicatorStrList.add(indicatorIdNameMap.get(indicatorId));
        //                }
        //            });
        //            vo.setIndicatorStrList(indicatorStrList);
        //
        //            //查询rv_activity_profile_result 用户人才档案匹配活动结果表
        //            long finishedUserCount = activityProfileResultRepo.findFinishedUserCount(orgId, actProfileId);
        //            vo.setFinishedUserCount(finishedUserCount);
        //        }
        //        //查询总人数rv_activity_participation_member
        //        long totalUserCount = rvActivityParticipationMemberRepo.findTotalUserCount(orgId, actProfileId);
        //        vo.setTotalUserCount(totalUserCount);
        //        return vo;


        return activityPerfDTO;
    }

    public void deletePerfActivity(String orgId, String userId, String id) {
        activityPerfMapper.deleteById(orgId, userId, id);
    }


    public PagingList<PerfUserPageUserVO> userList(PageRequest pageRequest, ActMemberUserCriteria searchParam) {
        String actId = searchParam.getActvId();
        Page<ActMemberUser> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        String orgId = searchParam.getOrgId();
        log.info("userList 参数={}", "actId=" + actId + ",orgId=" + orgId);
        if (StringUtils.isBlank(actId) || StringUtils.isBlank(orgId)) {
            log.warn("userList 参数异常={}", "actId or orgId is null.");
            return new PagingList<>();
        }
        //查询活动基础信息
        ActivityPerfPO perfPO = activityPerfMapper.selectById(orgId, actId);
        if (Objects.isNull(perfPO)) {
            throw new ApiException("apis.sptalentrv.perf.activity.null");
        }

        //        // 获取绩效信息
        //        Map<String, PerfPeriodPO> perfPeriodPOMap = getPeriodMap(perfPO);

        // 获取绩效结果
        Map<String, String> resultConfMap = getPerfResultConfMap(orgId, actId);

        //        // 获取绩效等级
        //        Map<Integer, String> gradesMap = getPerfGradeMap(orgId);

        //查询当前项目的人员数据
        return getPerfUserPageUserVOPagingList(perfPO, searchParam, page, resultConfMap);
    }

    public Map<Integer, PerfGradePO> getPerfGradeMap(String orgId) {
        Collection<PerfGradePO> perfGrades = perfGradeMapper.selectByOrgIdInState(orgId);
        Map<Integer, PerfGradePO> gradesMap = new HashMap<>(8);
        if (CollectionUtils.isNotEmpty(perfGrades)) {
            gradesMap = StreamUtil.list2map(perfGrades, PerfGradePO::getGradeValue);
        }
        return gradesMap;
    }

    public Map<String, String> getPerfResultConfMap(String orgId, String actId) {
        List<ActivityPerfResultConfPO> resultConfs = activityPerfResultConfMapper.selectByActivityId(orgId, actId);
        Map<String, String> resultConfMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(resultConfs)) {
            resultConfMap = StreamUtil.list2map(resultConfs, ActivityPerfResultConfPO::getId,
                    ActivityPerfResultConfPO::getResultName);
        }
        return resultConfMap;
    }

    public Map<String, PerfPeriodPO> getPeriodMap(ActivityPerfPO perfPO) {
        List<String> periodIds = new ArrayList<>();
        Map<String, PerfPeriodPO> perfPeriodPOMap = new HashMap<>();
        if (StringUtils.isNotBlank(perfPO.getPeriodIds())) {
            periodIds = Lists.newArrayList(perfPO.getPeriodIds().split(";"));
            List<PerfPeriodPO> periods = perfPeriodMapper.selectByOrgIdAndIds(perfPO.getOrgId(), periodIds);
            if (CollectionUtils.isNotEmpty(periods)) {
                perfPeriodPOMap = StreamUtil.list2map(periods, PerfPeriodPO::getId);
            }
        }
        return perfPeriodPOMap;
    }

    @NotNull
    public PagingList<PerfUserPageUserVO> getPerfUserPageUserVOPagingList(ActivityPerfPO perfPO,
            ActMemberUserCriteria searchParam, Page<ActMemberUser> page, Map<String, String> resultConfMap) {
        String actId = searchParam.getActvId();
        String orgId = perfPO.getOrgId();


        PagingList<ActMemberUser> userInfos = aomActivityService.getActivityUserList(page, orgId, searchParam);
        if (CollectionUtils.isEmpty(userInfos.getDatas())) {
            return new PagingList<>();
        }

        List<String> userIds = StreamUtil.mapList(userInfos.getDatas(), ActMemberUser::getUserId);

        Map<String, PerfPeriodPO> perfPeriodPOMap = getPeriodMap(perfPO);
        Map<Integer, PerfGradePO> gradesMap = getPerfGradeMap(orgId);

        // 用户基本信息MAP
        List<ActivityPerfResultPO> perfUserResult = activityPerfResultMapper.selectByUserIdAndActId(orgId, actId,
                userIds);
        Map<String, ActivityPerfResultPO> userMap = StreamUtil.list2map(perfUserResult,
                ActivityPerfResultPO::getUserId);

        List<String> periodIds = new ArrayList<>(perfPeriodPOMap.keySet());
        List<PerfPO> perfPOS = perfMapper.selectByPeriodIdsAndUserIds(orgId, periodIds, userIds);
        // 计算用户绩效得分，以及是否达标
        Map<String, List<PerfPO>> userPerfMap = perfPOS.stream().collect(Collectors.groupingBy(PerfPO::getUserId));
        List<PerfUserPageUserVO> result = new ArrayList<>();
        for (ActMemberUser activityUser : userInfos.getDatas()) {
            String userId = activityUser.getUserId();
            PerfUserPageUserVO user = new PerfUserPageUserVO();
            BeanHelper.copyProperties(activityUser, user);

            user.setCompleteStatus(activityUser.getResultStatus());

            ActivityPerfResultPO perfUser = userMap.get(userId);
            if (perfUser != null) {
                if (perfPO.getEvalType() == PerfEvalTypeEnum.LEVEL.getCode()) {
                    if (StringUtils.isNotBlank(perfUser.getResultConfId())) {
                        // 设置评估结果
                        user.setEvalResult(resultConfMap.get(perfUser.getResultConfId()));
                    }
                } else {
                    user.setEvalResult(String.valueOf(perfUser.getResultScore()));
                }
                user.setQuilified(perfUser.getQualified());
            }

            // 计算用户绩效得分
            List<PerfUserPeriodResultVO> periodResultVOS = getPerfUserPeriodResultVOS(userPerfMap, userId, periodIds,
                    perfPeriodPOMap, gradesMap);
            user.setPeriodResult(periodResultVOS);
            result.add(user);
        }

        PagingList<PerfUserPageUserVO> getPagingList = new PagingList<>();
        getPagingList.setPaging(userInfos.getPaging());
        getPagingList.setDatas(result);

        return getPagingList;
    }


    // 获取用户绩效得分
    @Nullable
    private List<PerfUserPeriodResultVO> getPerfUserPeriodResultVOS(Map<String, List<PerfPO>> userPerfMap,
            String userId, List<String> periodIds, Map<String, PerfPeriodPO> perfPeriodPOMap,
            Map<Integer, PerfGradePO> gradesMap) {
        List<PerfPO> userPerfs = userPerfMap.get(userId);
        if (CollectionUtils.isEmpty(userPerfs)) {
            return new ArrayList<>();
        }

        Map<String, PerfPO> perfMap = StreamUtil.list2map(userPerfs, PerfPO::getPeriodId);
        List<PerfUserPeriodResultVO> periodResultVOS = new ArrayList<>();
        // 设置绩效扩展字段
        for (String periodId : periodIds) {
            PerfUserPeriodResultVO periodResultVO = new PerfUserPeriodResultVO();
            if (perfPeriodPOMap.containsKey(periodId)) {
                PerfPeriodPO periodPO = perfPeriodPOMap.get(periodId);
                periodResultVO.setPeriodName(periodPO.getPeriodName());
                periodResultVO.setPeriodId(periodId);
                if (perfMap.containsKey(periodId)) {
                    PerfPO perf = perfMap.get(periodId);
                    BigDecimal score = perf.getPerfPoint();
                    String scoreStr = score == null ? "" : score.stripTrailingZeros().toPlainString();
                    if (gradesMap.containsKey(perf.getPeriodLevel())) {
                        if (StringUtils.isNotBlank(scoreStr)){
                            scoreStr = "("+scoreStr+")";
                        }
                        periodResultVO.setPeriodResult(
                            Optional.ofNullable(gradesMap.get(perf.getPeriodLevel()).getGradeName()).orElse("") + scoreStr);
                    }else{
                        periodResultVO.setPeriodResult(scoreStr);
                    }
                }
            }
            periodResultVOS.add(periodResultVO);
        }
        return periodResultVOS;
    }


    // 活动计算
    public void calUserPerf(String orgId, String opUserId, String id) {
        if (StringUtils.isBlank(id)) {
            throw new ApiException("apis.sptalentrv.perf.activity.id.notnull");
        }

        ActivityPerfPO activityPerfPO = activityPerfMapper.selectById(orgId, id);
        if (activityPerfPO == null) {
            // 绩效活动不存在
            throw new ApiException("apis.sptalentrv.perf.activity.null");
        }

        String calCacheKey = String.format(RedisKeys.CACHE_ACT_PERF_CAL, orgId, id);
        //防止并发计算同一活动
        if (lockService.tryLock(calCacheKey, 10, TimeUnit.MINUTES)) {
            //记录开始计算
            XpdCalcBatchDTO calcBatch = xpdCalcLogRepo.newCalcBatch(orgId, CalcLogTypeEnum.ACT_PREF.getCode(), id);
            try {
                // 得分方式
                if (activityPerfPO.getEvalType() == PerfEvalTypeEnum.SCORE.getCode()) {
                    calByScore(orgId, opUserId, activityPerfPO);
                }

                // 等级方式
                if (activityPerfPO.getEvalType() == PerfEvalTypeEnum.LEVEL.getCode()) {
                    calByLevel(orgId, opUserId, activityPerfPO);
                }
                xpdCalcLogRepo.endCalcLog(calcBatch.getId(), true);
            } catch (Exception e) {
                log.error("计算绩效活动结果异常actvId={},err=", id, e);
                xpdCalcLogRepo.endCalcLog(calcBatch.getId(), false);
            } finally {
                //记录结束计算
                lockService.unLock(calCacheKey);
            }
        }
    }

    /**
     * 计算用户绩效得分 , 绩效评分方式是 绩效等级时使用。
     * 1、计算绩效活动等级结果，入库
     * 2、推送结果数据给活动模型
     *
     * @param opUserId
     * @param activityPerfPO
     * @param orgId
     */
    private void calByLevel(String orgId, String opUserId, ActivityPerfPO activityPerfPO) {
        List<ActivityPerfResultConfPO> perfResultConfPOS = activityPerfResultConfMapper.selectByActivityId(
                activityPerfPO.getOrgId(), activityPerfPO.getId());
        if (CollectionUtils.isEmpty(perfResultConfPOS)) {
            log.info("未配置评估结果，{}", activityPerfPO.getId());
            return;
        }

        if (StringUtils.isBlank(activityPerfPO.getPeriodIds())) {
            log.info("未绑定绩效ID，{}", activityPerfPO.getId());
        }
        List<String> periodIds = Lists.newArrayList(activityPerfPO.getPeriodIds().split(";"));

        ActMemberUserCriteria searchParam = new ActMemberUserCriteria();
        searchParam.setOrgId(orgId);
        searchParam.setActvId(activityPerfPO.getId());

        int size = 500;
        int current = 1;
        boolean flag = true;
        while (flag) {
            Page<ActMemberUser> pageable = new Page<>(current, size);
            PagingList<ActMemberUser> userInfos = aomActivityService.getActivityUserList(pageable, orgId, searchParam);

            // 业务处理
            if (userInfos != null && CollectionUtils.isNotEmpty(userInfos.getDatas())) {
                List<String> uids = StreamUtil.mapList(userInfos.getDatas(), ActMemberUser::getUserId);
                calUserResultByLevel(orgId, opUserId, activityPerfPO, uids, periodIds, perfResultConfPOS);
            }
            if (userInfos != null && CollectionUtils.isNotEmpty(userInfos.getDatas())
                    && userInfos.getDatas().size() >= size) {
                current++;
                continue;
            }
            flag = false;
        }

    }

    private void calUserResultByLevel(String orgId, String opUserId, ActivityPerfPO activityPerfPO, List<String> uids,
            List<String> periodIds, List<ActivityPerfResultConfPO> perfResultConfPOS) {
        List<ActivityPerfResultPO> activityPerfResultPOS = new ArrayList<>();
        // 获取用户绩效数据
        List<PerfPO> perfPOS = perfMapper.selectByPeriodIdsAndUserIds(orgId, periodIds, uids);
        // 计算用户绩效得分，以及是否达标
        Map<String, List<UserIndicatorPushDataDTO.UserPeriodResult>> userPeriodResultMap = getUserPeriodResult(orgId,
                activityPerfPO, perfPOS);

        List<ActivityPerfResultConfDTO> spRuleBeans = new ArrayList<>();
        perfResultConfPOS.forEach(e -> {
            ActivityPerfResultConfDTO activityPerfResultConfDTO = new ActivityPerfResultConfDTO();
            BeanHelper.copyProperties(e, activityPerfResultConfDTO);
            activityPerfResultConfDTO.setRuleBean(BeanHelper.json2Bean(e.getRuleConf(), SpRuleBean.class));
            spRuleBeans.add(activityPerfResultConfDTO);
        });

        // 计算规则
        RuleMainBase ruleMainBase = new RuleMainBase();
        ruleMainBase.setOrgId(orgId);
        ruleMainBase.setBizId(activityPerfPO.getId());
        spRuleService.calcFirstMatch(ruleMainBase, uids, spRuleBeans, uid -> uid,
                ActivityPerfResultConfDTO::getRuleBean, (uid, conf) -> {
                    // 达标
                    // 组装用户计算结果
                    ActivityPerfResultPO activityPerfResultPO = new ActivityPerfResultPO();
                    activityPerfResultPO.setId(ApiUtil.getUuid());
                    activityPerfResultPO.setOrgId(orgId);
                    activityPerfResultPO.setUserId(uid);
                    activityPerfResultPO.setResultConfId(conf.getId());
                    activityPerfResultPO.setQualified(conf.getQualified());
                    activityPerfResultPO.setResultScore(conf.getScore());
                    activityPerfResultPO.setActvPerfId(activityPerfPO.getId());
                    activityPerfResultPO.setCreateTime(LocalDateTime.now());
                    activityPerfResultPO.setUpdateTime(LocalDateTime.now());
                    activityPerfResultPO.setCreateUserId(opUserId);
                    activityPerfResultPO.setUpdateUserId(opUserId);
                    activityPerfResultPOS.add(activityPerfResultPO);
                });

        // 设置未参与计算的用户为未达标结果
        setUnQualifiedResult(orgId, opUserId, activityPerfPO.getId(), activityPerfResultPOS, uids);

        // 更新或者插入计算结果
        saveAndUpdateResult(orgId, activityPerfPO.getId(), activityPerfResultPOS);

        Map<String, String> confMap = getPerfResultConfMap(orgId, activityPerfPO.getId());

        activityPerfResultPOS.forEach(e -> {
            log.info("推送用户绩效结果数据，{}", e.getResultConfId());
            UserIndicatorPushDataDTO userIndicatorPushDataDTO = new UserIndicatorPushDataDTO();
            userIndicatorPushDataDTO.setQualified(e.getQualified());
            if (StringUtils.isNotBlank(e.getResultConfId()) && confMap.containsKey(e.getResultConfId())) {
                userIndicatorPushDataDTO.setResultConfName(confMap.get(e.getResultConfId()));
                userIndicatorPushDataDTO.setResultConfId(e.getResultConfId());
            }
            userIndicatorPushDataDTO.setUserPeriodResults(userPeriodResultMap.get(e.getUserId()));
            pushToAom(activityPerfPO, orgId, e, userIndicatorPushDataDTO);
        });
    }

    @NotNull
    private Map<String, List<UserIndicatorPushDataDTO.UserPeriodResult>> getUserPeriodResult(String orgId,
            ActivityPerfPO activityPerfPO, List<PerfPO> perfPOS) {
        Map<String, List<PerfPO>> userPerfMap = perfPOS.stream().collect(Collectors.groupingBy(PerfPO::getUserId));
        Map<String, List<UserIndicatorPushDataDTO.UserPeriodResult>> userPeriodResultMap = new HashMap<>();

        Map<String, PerfPeriodPO> periodPOMap = getPeriodMap(activityPerfPO);
        Map<Integer, PerfGradePO> perfGradeMap = getPerfGradeMap(orgId);

        userPerfMap.forEach((k, v) -> {
            if (CollectionUtils.isNotEmpty(v)) {
                List<UserIndicatorPushDataDTO.UserPeriodResult> userPeriodResults = new ArrayList<>();
                v.forEach(perf -> {
                    UserIndicatorPushDataDTO.UserPeriodResult userPeriodResult = new UserIndicatorPushDataDTO.UserPeriodResult();
                    if (!periodPOMap.isEmpty() && periodPOMap.containsKey(perf.getPeriodId())) {
                        PerfPeriodPO periodPO = periodPOMap.get(perf.getPeriodId());
                        userPeriodResult.setPeriodName(periodPO.getPeriodName());
                        userPeriodResult.setPeriodId(perf.getPeriodId());
                    }
                    if (!perfGradeMap.isEmpty() && perfGradeMap.containsKey(perf.getPeriodLevel())) {
                        PerfGradePO perfGradePO = perfGradeMap.get(perf.getPeriodLevel());
                        userPeriodResult.setGradeName(perfGradePO.getGradeName());
                        userPeriodResult.setGradeId(perfGradePO.getId());
                        userPeriodResult.setGradeOrderIndex(perfGradePO.getOrderIndex());
                        userPeriodResult.setGradeValue(perfGradePO.getGradeValue());
                    }
                    userPeriodResult.setScore(perf.getPerfPoint());
                    userPeriodResult.setTotalScore(perf.getPerfScore());
                    userPeriodResults.add(userPeriodResult);
                });
                userPeriodResultMap.put(k, userPeriodResults);
            }
        });
        return userPeriodResultMap;
    }

    public List<PerfPeriodPO> getAllPeriodList(String orgId) {
        return perfPeriodMapper.selectByOrgId(orgId);
    }

    public List<PerfPeriodPO> getPeriodList(String orgId, String actvId) {
        ActivityPerfPO activityPerfPO = activityPerfMapper.selectById(orgId, actvId);
        if (activityPerfPO == null) {
            return Collections.emptyList();
        }
        List<String> periodIds = Lists.newArrayList(activityPerfPO.getPeriodIds().split(";"));
        return perfPeriodMapper.selectByOrgIdAndIds(orgId, periodIds);
    }

    public List<PerfPO> getUserPeriod(String orgId, List<String> periodIds, List<String> uids) {
        return perfMapper.selectByPeriodIdsAndUserIds(orgId, periodIds, uids);
    }

    public List<PerfGradePO> getPerfGrades(String orgId) {
        Collection<PerfGradePO> perfGrades = perfGradeMapper.selectByOrgId(orgId);
        if (CollectionUtils.isNotEmpty(perfGrades)) {
            return perfGrades.stream().toList();
        }
        return Collections.emptyList();
    }

    private void calByScore(String orgId, String opUserId, ActivityPerfPO activityPerfPO) {
        // 获取绩效权重
        List<ActivityPerfConfPO> perfConfPOS = activityPerfConfMapper.selectByActivityId(activityPerfPO.getOrgId(),
                activityPerfPO.getId());
        if (CollectionUtils.isEmpty(perfConfPOS)) {
            log.info("未绑定绩效权重，{}", activityPerfPO.getId());
            return;
        }
        Map<String, BigDecimal> weightMap = StreamUtil.list2map(perfConfPOS, ActivityPerfConfPO::getPeriodId,
                ActivityPerfConfPO::getWeight);

        ActMemberUserCriteria searchParam = new ActMemberUserCriteria();
        searchParam.setOrgId(orgId);
        searchParam.setActvId(activityPerfPO.getId());

        if (StringUtils.isBlank(activityPerfPO.getPeriodIds())) {
            log.info("未绑定绩效ID，{}", activityPerfPO.getId());
        }
        List<String> periodIds = Lists.newArrayList(activityPerfPO.getPeriodIds().split(";"));

        int size = 500;
        int current = 1;
        boolean flag = true;
        while (flag) {
            Page<ActMemberUser> pageable = new Page<>(current, size);
            PagingList<ActMemberUser> userInfos = aomActivityService.getActivityUserList(pageable, orgId, searchParam);

            // 业务处理
            if (userInfos != null && CollectionUtils.isNotEmpty(userInfos.getDatas())) {
                List<String> uids = StreamUtil.mapList(userInfos.getDatas(), ActMemberUser::getUserId);
                calUserResultByScore(opUserId, activityPerfPO, uids, orgId, periodIds, weightMap);
            }
            if (userInfos != null && CollectionUtils.isNotEmpty(userInfos.getDatas())
                    && userInfos.getDatas().size() >= size) {
                current++;
                continue;
            }
            flag = false;
        }

    }

    /**
     * 计算用户绩效得分 , 绩效评分方式是 绩效得分时使用。
     * 1、计算绩效活动得分结果，入库
     * 2、推送结果数据给活动模型
     *
     * @param opUserId
     * @param activityPerfPO
     * @param uids
     * @param orgId
     * @param periodIds
     * @param weightMap
     */
    private void calUserResultByScore(String opUserId, ActivityPerfPO activityPerfPO, List<String> uids, String orgId,
            List<String> periodIds, Map<String, BigDecimal> weightMap) {
        List<ActivityPerfResultPO> activityPerfResultPOS = new ArrayList<>();
        // 获取用户绩效数据
        List<PerfPO> perfPOS = perfMapper.selectByPeriodIdsAndUserIds(orgId, periodIds, uids);
        // 计算用户绩效得分，以及是否达标
        Map<String, List<PerfPO>> userPerfMap = perfPOS.stream().collect(Collectors.groupingBy(PerfPO::getUserId));
        // 用户周期结果明细
        Map<String, List<UserIndicatorPushDataDTO.UserPeriodResult>> userPeriodResultMap = getUserPeriodResult(orgId,
                activityPerfPO, perfPOS);

        for (String userId : uids) {
            // 计算用户绩效得分
            List<PerfPO> userPerfs = userPerfMap.get(userId);

            // 组装用户周期结果明细
            if (CollectionUtils.isEmpty(userPerfs)) {
                continue;
            }
            // 过滤绩效分数为空的数据
            userPerfs = userPerfs.stream().filter(e -> e.getPerfPoint() != null).collect(Collectors.toList());
            Map<String, BigDecimal> userScoreMap = StreamUtil.list2map(userPerfs, PerfPO::getPeriodId,
                    PerfPO::getPerfPoint);
            BigDecimal userScore = BigDecimal.ZERO;

            for (String perf : periodIds) {
                BigDecimal score = BigDecimal.ZERO;
                if (userScoreMap.containsKey(perf) && weightMap.containsKey(perf)) {
                    score = userScoreMap.get(perf);
                }
                userScore = userScore.add(
                        score.multiply(weightMap.get(perf).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)));
            }

            if (userScore.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            int qualified = 0;
            if (userScore.compareTo(activityPerfPO.getScoreQualified()) >= 0) {
                qualified = 1;
            }

            // 组装用户计算结果
            ActivityPerfResultPO activityPerfResultPO = new ActivityPerfResultPO();
            activityPerfResultPO.setId(ApiUtil.getUuid());
            activityPerfResultPO.setOrgId(orgId);
            activityPerfResultPO.setUserId(userId);
            activityPerfResultPO.setResultScore(userScore);
            activityPerfResultPO.setQualified(qualified);
            activityPerfResultPO.setActvPerfId(activityPerfPO.getId());
            activityPerfResultPO.setCreateTime(LocalDateTime.now());
            activityPerfResultPO.setUpdateTime(LocalDateTime.now());
            activityPerfResultPO.setCreateUserId(opUserId);
            activityPerfResultPO.setUpdateUserId(opUserId);
            activityPerfResultPOS.add(activityPerfResultPO);

        }

        // 设置未参与计算的用户为未达标结果
        setUnQualifiedResult(orgId, opUserId, activityPerfPO.getId(), activityPerfResultPOS, uids);

        // 更新或者插入计算结果
        saveAndUpdateResult(orgId, activityPerfPO.getId(), activityPerfResultPOS);

        activityPerfResultPOS.forEach(e -> {
            log.info("推送用户绩效结果数据，{}", e.getResultConfId());
            UserIndicatorPushDataDTO userIndicatorPushDataDTO = new UserIndicatorPushDataDTO();
            userIndicatorPushDataDTO.setQualified(e.getQualified());
            userIndicatorPushDataDTO.setUserPeriodResults(userPeriodResultMap.get(e.getUserId()));
            pushToAom(activityPerfPO, orgId, e, userIndicatorPushDataDTO);
        });

    }

    private void setUnQualifiedResult(String orgId, String opUserId, String acvId,
            List<ActivityPerfResultPO> activityPerfResultPOS, List<String> uids) {
        List<String> exiteUids = StreamUtil.mapList(activityPerfResultPOS, ActivityPerfResultPO::getUserId);

        if (CollectionUtils.isEmpty(uids)) {
            return;
        }
        uids.forEach(uid -> {
            if (!exiteUids.contains(uid)) {
                ActivityPerfResultPO activityPerfResultPO = new ActivityPerfResultPO();
                activityPerfResultPO.setId(ApiUtil.getUuid());
                activityPerfResultPO.setOrgId(orgId);
                activityPerfResultPO.setUserId(uid);
                activityPerfResultPO.setResultScore(BigDecimal.ZERO);
                activityPerfResultPO.setQualified(0);
                activityPerfResultPO.setActvPerfId(acvId);
                activityPerfResultPO.setCreateTime(LocalDateTime.now());
                activityPerfResultPO.setUpdateTime(LocalDateTime.now());
                activityPerfResultPO.setCreateUserId(opUserId);
                activityPerfResultPO.setUpdateUserId(opUserId);
                activityPerfResultPOS.add(activityPerfResultPO);
            }
        });

    }

    private List<UserIndicatorPushDataDTO.UserPeriodResult> convertToPushUserPeriodResult(ActivityPerfPO activityPerfPO,
            List<PerfPO> userPerfs) {
        if (CollectionUtils.isEmpty(userPerfs)) {
            return new ArrayList<>();
        }

        Map<String, PerfPeriodPO> perfPeriodPOMap = getPeriodMap(activityPerfPO);
        Map<Integer, PerfGradePO> gradeMap = getPerfGradeMap(activityPerfPO.getOrgId());

        List<UserIndicatorPushDataDTO.UserPeriodResult> list = new ArrayList<>(userPerfs.size());
        userPerfs.forEach(e -> {
            UserIndicatorPushDataDTO.UserPeriodResult userPeriodResult = new UserIndicatorPushDataDTO.UserPeriodResult();
            userPeriodResult.setPeriodId(e.getPeriodId());
            if (perfPeriodPOMap.containsKey(e.getPeriodId())) {
                PerfPeriodPO perfPeriodPO = perfPeriodPOMap.get(e.getPeriodId());
                userPeriodResult.setTotalScore(perfPeriodPO.getScoreTotal());
                userPeriodResult.setPeriodName(perfPeriodPO.getPeriodName());
            }
            if (gradeMap.containsKey(e.getPeriodLevel())) {
                PerfGradePO perfGradePO = gradeMap.get(e.getPeriodLevel());
                userPeriodResult.setGradeId(perfGradePO.getId());
                userPeriodResult.setGradeName(perfGradePO.getGradeName());
                userPeriodResult.setGradeValue(perfGradePO.getGradeValue());
                userPeriodResult.setGradeOrderIndex(perfGradePO.getOrderIndex());
            }
            userPeriodResult.setScore(e.getPerfPoint());
            list.add(userPeriodResult);
        });
        return list;


    }

    /**
     * 获取绩效活动总分
     * 绩效得分：总分 = 各周期得分 * 权重
     * 绩效等级：总分 = 绩效结果等级最高的得分为总分
     *
     * @param orgId
     * @param actvId
     * @return
     */
    public BigDecimal getTotalScore(String orgId, String actvId) {
        ActivityPerfPO activityPerfPO = activityPerfMapper.selectById(orgId, actvId);
        Validate.isNotNull(activityPerfPO, ExceptionKeys.ACTIVITY_NOT_EXIST);
        if (activityPerfPO.getEvalType() == PerfEvalTypeEnum.SCORE.getCode()) {
            BigDecimal totalScore = BigDecimal.ZERO;
            List<ActivityPerfConfPO> perfConfPOS = activityPerfConfMapper.selectByActivityId(orgId, actvId);
            if (CollectionUtils.isEmpty(perfConfPOS)) {
                log.info("未绑定绩效权重，{}", activityPerfPO.getId());
                return totalScore;
            }

            Map<String, BigDecimal> weightMap = StreamUtil.list2map(perfConfPOS, ActivityPerfConfPO::getPeriodId,
                    ActivityPerfConfPO::getWeight);

            List<String> periodIds = Lists.newArrayList(activityPerfPO.getPeriodIds().split(";"));
            List<PerfPeriodPO> perfPOS = perfPeriodMapper.selectByOrgIdAndIds(orgId, periodIds);
            if (CollectionUtils.isNotEmpty(perfPOS)) {
                for (PerfPeriodPO perf : perfPOS) {
                    if (weightMap.containsKey(perf.getId())) {
                        BigDecimal perfScore = perf.getScoreTotal();
                        if (perf.getScoreTotal() == null) {
                            // 如果周期得分不存在，则默认为0分
                            perfScore = BigDecimal.ZERO;
                        }
                        totalScore = totalScore.add(perfScore.multiply(
                                weightMap.get(perf.getId()).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)));
                    }
                }
            }
            return totalScore;
        }

        if (activityPerfPO.getEvalType() == PerfEvalTypeEnum.LEVEL.getCode()) {
            List<ActivityPerfResultConfPO> perfResultConfPOS = activityPerfResultConfMapper.selectByActivityId(orgId,
                    actvId);
            if (CollectionUtils.isNotEmpty(perfResultConfPOS)) {
                return perfResultConfPOS.get(0).getScore();
            }
            return BigDecimal.ZERO;
        }

        return BigDecimal.ZERO;
    }

    /**
     * 推送结果数据给AOM
     * 1、结果基础信息
     * 2、用户绩效结果明细
     *
     * @param activityPerfPO           活动绩效PO
     * @param orgId                    机构ID
     * @param e                        绩效活动结果，单个用户的结果
     * @param userIndicatorPushDataDTO 绩效指标结果明细
     */

    private void pushToAom(ActivityPerfPO activityPerfPO, String orgId, ActivityPerfResultPO e,
            UserIndicatorPushDataDTO userIndicatorPushDataDTO) {
        Activity activity = activityService.findById(orgId, activityPerfPO.getId());
        if (activity == null) {
            throw new ApiException("apis.aom.common.id.invalid");
        }
        BigDecimal totalScore = getTotalScore(orgId, activityPerfPO.getId());
        Actor actor = new Actor();
        actor.setUserId(e.getUserId());

        TargetObject targetObject = new TargetObject();
        targetObject.setTargetId(activityPerfPO.getId());
        targetObject.setTargetType(UacdTypeEnum.ACTV_PERF.getRegId());
        targetObject.setSourceId(activity.getSourceId());
        targetObject.setSourceType(UacdTypeEnum.PRJ_XPD.getRegId());

        AssessmentActivityResult assessmentActivityResult = new AssessmentActivityResult();
        assessmentActivityResult.setResultStatus(2);
        assessmentActivityResult.setCompletedTime(new Date());
        assessmentActivityResult.setLastStudyTime(new Date());
        assessmentActivityResult.setScore(e.getResultScore());
        assessmentActivityResult.setTotalScore(totalScore);
        //        if (userIndicatorPushDataDTO != null) {
        //            assessmentActivityResult.setExt(
        //                JSONObject.parseObject(BeanHelper.bean2Json(userIndicatorPushDataDTO, JsonInclude.Include.NON_NULL)));
        //        }

        List<IndexResult> indexResults = new ArrayList<>();
        IndexResult indexResult = new IndexResult();
        indexResult.setObjectiveModeId(activityPerfPO.getModelId());
        indexResult.setObjectiveId(activityPerfPO.getIndicatorId());
        indexResult.setObjectiveType(5);
        indexResult.setObjectiveScore(e.getResultScore());
        indexResult.setObjectiveTotalScore(totalScore);
        indexResult.setObjectiveResult(e.getQualified());
        indexResult.setExt(
                JSONObject.parseObject(BeanHelper.bean2Json(userIndicatorPushDataDTO, JsonInclude.Include.NON_NULL)));
        if (e.getQualified() == 1) {
            indexResult.setObjectiveResult(3);
        }
        indexResults.add(indexResult);
        assessmentActivityResult.setIndexResults(indexResults);

        log.info("推送绩效结果数据给AOM，actor,{},target,{},{}",
                BeanHelper.bean2Json(actor, JsonInclude.Include.NON_NULL),
                BeanHelper.bean2Json(targetObject, JsonInclude.Include.NON_NULL),
                BeanHelper.bean2Json(assessmentActivityResult, JsonInclude.Include.NON_NULL));
        resultRollUpService.rollUpAssessmentActivityResult(orgId, ActionEnum.COMPLETED, actor, targetObject,
                assessmentActivityResult);
    }

    /**
     * 保存或者更新绩效结果数据, 数据库操作
     * 如果结果不存在，走新增
     * 如果结果存在，走更新
     *
     * @param orgId
     * @param actId
     * @param activityPerfResultPOS
     */
    private void saveAndUpdateResult(String orgId, String actId, List<ActivityPerfResultPO> activityPerfResultPOS) {
        if (CollectionUtils.isEmpty(activityPerfResultPOS)) {
            log.info("未计算用户绩效得分，{}", actId);
            return;
        }
        List<String> userIds = StreamUtil.mapList(activityPerfResultPOS, ActivityPerfResultPO::getUserId);
        List<ActivityPerfResultPO> exiteList = activityPerfResultMapper.selectByUserIdAndActId(orgId, actId, userIds);
        if (CollectionUtils.isEmpty(exiteList)) {
            // 未找到用户，直接更新
            activityPerfResultMapper.insertList(activityPerfResultPOS);
            return;
        }

        Map<String, ActivityPerfResultPO> exiteMap = StreamUtil.list2map(exiteList, ActivityPerfResultPO::getUserId);

        List<ActivityPerfResultPO> insertList = new ArrayList<>();
        List<ActivityPerfResultPO> updateList = new ArrayList<>();
        // 计算更新数据
        for (ActivityPerfResultPO po : activityPerfResultPOS) {
            if (exiteMap.containsKey(po.getUserId())) {
                ActivityPerfResultPO updatePO = exiteMap.get(po.getUserId());

                updatePO.setResultConfId(po.getResultConfId());
                updatePO.setResultScore(po.getResultScore());
                updatePO.setQualified(po.getQualified());
                updatePO.setUpdateUserId(po.getUpdateUserId());
                updatePO.setUpdateTime(po.getUpdateTime());

                updateList.add(updatePO);
                continue;
            }
            insertList.add(po);
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            activityPerfResultMapper.insertList(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            activityPerfResultMapper.updateBatch(updateList);
        }

    }

    private void deleteActivityConfList(String orgId, String perfActivityId) {
        activityPerfConfMapper.deleteByActivityId(orgId, perfActivityId);
        activityPerfResultConfMapper.deleteByActivityId(orgId, perfActivityId);
    }

    // 创建绩效得分权重，绩效评估结果配置

    private void addActivityConfList(String orgId, String opUserId, PerfActivityDTO perfActivityDTO,
            String activityPerfId) {
        // 创建绩效活动绩效周期得分权重
        List<ActivityPerfConfPO> confList = BeanCopierUtil.convertList(perfActivityDTO.getConfList(), PerfConfDTO.class,
                ActivityPerfConfPO.class);
        if (CollectionUtils.isNotEmpty(confList)) {
            for (ActivityPerfConfPO conf : confList) {
                conf.setActvPerfId(activityPerfId);
                conf.setId(ApiUtil.getUuid());
                conf.setOrgId(orgId);
                conf.setCreateTime(LocalDateTime.now());
                conf.setUpdateTime(LocalDateTime.now());
                conf.setCreateUserId(opUserId);
                conf.setUpdateUserId(opUserId);
                conf.setDeleted(DeleteEnum.NOT_DELETED.getCode());
            }
            activityPerfConfMapper.insertList(confList);
        }

        // 创建绩效活动评估结果配置

        if (CollectionUtils.isNotEmpty(perfActivityDTO.getResultConfDTOS())) {
            List<ActivityPerfResultConfPO> resultConfList = new ArrayList<>();
            int orderNum = 1;
            for (PerfResultConfDTO confDto : perfActivityDTO.getResultConfDTOS()) {
                ActivityPerfResultConfPO conf = new ActivityPerfResultConfPO();
                BeanHelper.copyProperties(confDto, conf);

                RuleMainBase ruleMainBase = new RuleMainBase();
                ruleMainBase.setOrgId(orgId);
                ruleMainBase.setBizId(confDto.getActvPerfId());
                log.info("校验规则，{}", BeanHelper.bean2Json(confDto.getRuleConfBean(), JsonInclude.Include.NON_NULL));
                spRuleService.checkRule(ruleMainBase, confDto.getRuleConfBean());
                log.info("校验规则 成功，{}",
                        BeanHelper.bean2Json(confDto.getRuleConfBean(), JsonInclude.Include.NON_NULL));

                conf.setActvPerfId(activityPerfId);
                conf.setId(ApiUtil.getUuid());
                conf.setOrgId(orgId);
                conf.setCreateTime(LocalDateTime.now());
                conf.setUpdateTime(LocalDateTime.now());
                conf.setCreateUserId(opUserId);
                conf.setUpdateUserId(opUserId);
                conf.setOrderNum(orderNum++);
                conf.setDeleted(DeleteEnum.NOT_DELETED.getCode());
                //                conf.setRuleDisplay(spRuleService.calcRuleDisplay(ruleMainBase, confDto.getRuleConfBean()));
                conf.setRuleConf(BeanHelper.bean2Json(confDto.getRuleConfBean(), JsonInclude.Include.NON_NULL));
                conf.setScore(confDto.getScore() == null ? BigDecimal.ZERO : confDto.getScore());
                conf.setQualified(confDto.getQualified() == null ? 0 : confDto.getQualified());
                resultConfList.add(conf);
            }
            activityPerfResultConfMapper.insertList(resultConfList);
        }
    }

    public RvPerfAssessment4Get getPerfActivitySimpleDetail(String orgId, String id) {
        ActivityPerfPO activityPerfPO = activityPerfMapper.selectById(orgId, id);
        Validate.isNotNull(activityPerfPO, ExceptionKeys.ACTIVITY_NOT_EXIST);
        RvPerfAssessment4Get activityVO = new RvPerfAssessment4Get();
        activityVO.setName(activityPerfPO.getActvName());

        Map<String, PerfPeriodPO> perfPeriodPOMap = getPeriodMap(activityPerfPO);
        if (!perfPeriodPOMap.isEmpty()) {
            List<Object> list = new ArrayList<>();
            perfPeriodPOMap.forEach((k, v) -> {
                Map<String, Object> periodMap = new HashMap<>();
                periodMap.put("id", v.getId());
                periodMap.put("name", v.getPeriodName());
                list.add(periodMap);
            });
            AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
            amSlDrawer4RespDTO.setDatas(list);
            activityVO.setPeriodids(amSlDrawer4RespDTO);
        }

        activityVO.setEvaltype(String.valueOf(activityPerfPO.getEvalType()));
        activityVO.setEvaltimetype(String.valueOf(activityPerfPO.getEvalTimeType()));

        // 设置评估时间
        setEvalTime(orgId, id, activityPerfPO, activityVO);

        Pair<String, Long> prjAndPartId = xpdService.getPartAndPrjId(orgId, id);
        List<String> actUserIds = rvActivityParticipationMemberRepo.findAllUserIdByActId(orgId, prjAndPartId.getKey());
        long finishCount;
        if (CollectionUtils.isEmpty(actUserIds)) {
            finishCount = 0L;
        } else {
            //            long finishCount = activityPerfResultMapper.selectFinishCount(orgId, id);
            //finishCount = activityPerfResultMapper.selectFinishCountRange(orgId, id, actUserIds);
            finishCount = rvActivityParticipationMemberRepo.countByActvId(orgId, prjAndPartId.getKey(), id);
        }
        long totalCount = rvActivityParticipationMemberRepo.findTotalUserCount(orgId, prjAndPartId.getKey(),
                prjAndPartId.getValue());
        activityVO.setActvnumber(finishCount + "/" + totalCount);
        return activityVO;
    }

    private void setEvalTime(String orgId, String id, ActivityPerfPO activityPerfPO, RvPerfAssessment4Get activityVO) {
        // 定时评估
        if (activityPerfPO.getEvalTimeType() == 2 && activityPerfPO.getEvalTime() != null) {
            activityVO.setEvaltime(DateUtil.toDate(activityPerfPO.getEvalTime()));
        }

        // 动态评估
        if (activityPerfPO.getEvalTimeType() == 1) {
            XpdCalcLogPO xpdCalcLogPO = xpdCalcLogRepo.findLatestByProfId(orgId, CalcLogTypeEnum.ACT_PREF.getCode(),
                    id);
            if (xpdCalcLogPO != null) {
                if (xpdCalcLogPO.getEndTime() != null) {
                    activityVO.setEvaltime(DateUtil.toDate(xpdCalcLogPO.getEndTime()));
                } else if (xpdCalcLogPO.getStartTime() != null) {
                    activityVO.setEvaltime(DateUtil.toDate(xpdCalcLogPO.getStartTime()));
                }
            }
        }
    }

    /**
     * 获取绩效下的指标
     *
     * @return
     */
    public List<ActIndicator> getPerfIndicators(String orgId, String modelId) {
        ModelInfo modelInfo = spTalentSdFacade.getModelInfo(orgId, modelId);
        if (modelInfo == null) {
            throw new ApiException(ExceptionKeys.XPD_MODEL_NOT_EXIST);
        }
        List<ModelRequireBaseInfo> dimList = modelInfo.getDms();
        if (CollectionUtils.isEmpty(dimList)) {
            throw new ApiException(ExceptionKeys.XPD_MODEL_DIM_NOT_EXIST);
        }

        ModelRequireBaseInfo perfDim = findPerfDim(dimList);
        if (perfDim == null) {
            throw new ApiException(ExceptionKeys.XPD_MODEL_PERF_DIM_NOT_EXIST);
        }

        List<ModelRequireDetailInfo> inds = collectAllIndicators(perfDim);
        List<ActIndicator> actIndicators = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inds)) {
            inds = inds.stream().filter(ind -> ind.getItemType() != 5).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(inds)) {
            inds.forEach(ind -> {
                ActIndicator actIndicator = new ActIndicator();
                BeanHelper.copyProperties(ind, actIndicator);
                // 0-普通、1-能力、2-技能、3-知识、4-任务  5-分类（暂未使用）
                if (ind.getItemType() == 1 || ind.getItemType() == 2 || ind.getItemType() == 3) {
                    if (ind.getItem() != null) {
                        actIndicator.setItemName(ind.getItem().getName());
                    }
                }
                if (ind.getItemType() == 0) {
                    actIndicator.setItemName(ind.getItemValue());
                }
                if (ind.getItemType() == 4) {
                    if (ind.getRt() != null) {
                        actIndicator.setItemName(ind.getRt().getName());
                    }
                }

                actIndicators.add(actIndicator);
            });
        }
        return actIndicators;
    }

    public ModelRequireBaseInfo findPerfDim(List<ModelRequireBaseInfo> list) {
        for (ModelRequireBaseInfo info : list) {
            if ("绩效".equals(info.getDmName())) {
                return info;
            }
            if (CollectionUtils.isNotEmpty(info.getChilds())) {
                ModelRequireBaseInfo childResult = findPerfDim(info.getChilds());
                if (childResult != null) {
                    return childResult;
                }
            }
        }
        return null;
    }

    public List<ModelRequireDetailInfo> collectAllIndicators(ModelRequireBaseInfo perfDim) {
        // 收集所有指标
        List<ModelRequireDetailInfo> allInds = new ArrayList<>();
        collectIndicators(perfDim, allInds);
        return allInds;
    }

    private void collectIndicators(ModelRequireBaseInfo baseInfo, List<ModelRequireDetailInfo> indsList) {
        // 递归当前维度下的所有指标
        collectSubIndicatorsByDim(baseInfo, indsList);

        // 便利当前维度下子维度
        List<ModelRequireBaseInfo> childs = baseInfo.getChilds();
        if (CollectionUtils.isNotEmpty(childs)) {
            for (ModelRequireBaseInfo child : childs) {
                collectIndicators(child, indsList);
            }
        }
    }

    /**
     * 递归收集维度下的所有指标,
     *
     * @param baseInfo
     * @param indsList
     */

    private void collectSubIndicatorsByDim(ModelRequireBaseInfo baseInfo, List<ModelRequireDetailInfo> indsList) {
        List<ModelRequireDetailInfo> inds = baseInfo.getDetails();
        if (CollectionUtils.isNotEmpty(inds)) {
            // 递归收集指标下的所有子指标,
            for (ModelRequireDetailInfo ind : inds) {
                collectSubIndicatorsByIndicator(ind, indsList);
            }
        }
    }

    /**
     * 递归收集指标下的所有子指标,
     *
     * @param ind
     * @param indsList
     */

    private void collectSubIndicatorsByIndicator(ModelRequireDetailInfo ind, List<ModelRequireDetailInfo> indsList) {
        indsList.add(ind);
        if (CollectionUtils.isNotEmpty(ind.getChilds())) {
            for (ModelRequireDetailInfo child : ind.getChilds()) {
                collectSubIndicatorsByIndicator(child, indsList);
            }
        }
    }


    public static PerfActivityService self() {
        return SpringContextHolder.getBean(PerfActivityService.class);
    }

    public void sendCalcMq(String orgId, String id, String optUserId) {
        log.info("perf Calc sendCalcMq 参数 orgId={}, perf={} optUserId={}", orgId, id, optUserId);
        PerfActivityCalcMqDTO perfActivityCalcMqDTO = new PerfActivityCalcMqDTO();
        perfActivityCalcMqDTO.setPerfProfId(id);
        perfActivityCalcMqDTO.setOrgId(orgId);
        perfActivityCalcMqDTO.setOptUserId(optUserId);

        // 打上开始计算的标识
        activityCalcComponent.startCalculate(id, CalcLogTypeEnum.ACT_PREF.getCode());

        rocketMqAclSender.send(TOPIC_PERF_ACTIVITY_CALC, JSON.toJSONString(perfActivityCalcMqDTO));
    }
}
