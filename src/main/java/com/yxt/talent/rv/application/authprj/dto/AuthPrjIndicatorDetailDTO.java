package com.yxt.talent.rv.application.authprj.dto;

import lombok.*;

import java.math.BigDecimal;

/**
 * 认证项目指标详细信息通用DTO
 * 用于统一处理概览统计和员工详细数据
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AuthPrjIndicatorDetailDTO {

    /**
     * 一级指标id
     */
    private String firstIndicatorId;
    
    /**
     * 一级指标名称
     */
    private String firstIndicatorName;

    /**
     * 二级指标id
     */
    private String secondIndicatorId;
    
    /**
     * 二级指标名称
     */
    private String secondIndicatorName;

    /**
     * 三级指标id
     */
    private String thirdIndicatorId;
    
    /**
     * 三级指标名称
     */
    private String thirdIndicatorName;

    /**
     * 来源活动id
     */
    private String refId;

    /**
     * 来源活动名称（数据来源）
     */
    private String refName;

    /**
     * 总分
     */
    private BigDecimal scoreTotal;

    /**
     * 得分
     */
    private BigDecimal score;
}
