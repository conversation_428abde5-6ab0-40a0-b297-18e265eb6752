package com.yxt.talent.rv.application.authprj.slot;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.base.bean.common.ActivityRelationBean;
import com.yxt.aom.base.bean.part.AomAddActivityMemberMsgBean;
import com.yxt.aom.base.custom.ActivityMemberChangePostProcessorCompo;
import com.yxt.aom.base.entity.part.ActivityParticipationMember;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.authprj.AuthPrjTodoAndMsgService;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 认证项目生命周期自定义接口
 */
@Slf4j
@RequiredArgsConstructor
@Component("activityMemberChangePostProcessorCompo4ProjAuthpj")
public class AuthPrjMemberChangePostProcessorCompo implements ActivityMemberChangePostProcessorCompo {

    private final AuthPrjTodoAndMsgService authPrjTodoService;
    private final AuthprjMapper authprjMapper;


    @Override
    @Auditing
    public void activityMemberAddPostProcessor(AomAddActivityMemberMsgBean bean) {
        log.info("activityMemberChangePostProcessorCompo4ProjAuthpj add = {}", bean.getBean().getActvId());
        if (CollectionUtils.isNotEmpty(bean.getMemberList())) {
            String orgId = bean.getBean().getOrgId();
            AuthprjPO authprjPO = authprjMapper.selectByAomPrjId(orgId, bean.getBean().getActvId());
            if (authprjPO == null) {
                throw new ApiException(ExceptionKeys.AUTHPRJ_NOT_EXIST);
            }

            List<String> userIds = bean.getMemberList().stream().map(ActivityParticipationMember::getUserId).toList();
            authPrjTodoService.addUserTodo(orgId, bean.getBean().getOptUserId(), authprjPO.getId(), userIds);
        }
    }

    @Override
    @Auditing
    public void activityMemberDel(AomAddActivityMemberMsgBean bean) {
        log.info("activityMemberChangePostProcessorCompo4ProjAuthpj del = {}", BeanHelper.bean2Json(bean, JsonInclude.Include.NON_NULL));
        if (bean.getBean() != null && CollectionUtils.isNotEmpty(bean.getBean().getUserIds())) {
            String orgId = bean.getBean().getOrgId();
            AuthprjPO authprjPO = authprjMapper.selectByAomPrjId(orgId, bean.getBean().getActvId());
            if (authprjPO == null) {
                throw new ApiException(ExceptionKeys.AUTHPRJ_NOT_EXIST);
            }
            List<String> userIds = bean.getBean().getUserIds();
            authPrjTodoService.deleteUserTodo(orgId, bean.getBean().getOptUserId(), authprjPO.getId(), userIds);
        }
    }

    @Override
    public void actvPrincipalChange(ActivityRelationBean activityRelationBean) {

    }
}
