package com.yxt.talent.rv.application.authprj;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.yxt.aom.activity.service.trace.ActivityRepeatService;
import com.yxt.aom.base.bean.arrange.ActivityArrangeItem4ActvMgr;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.entity.control.BaseActivityResult;
import com.yxt.aom.base.mapper.control.BaseActivityResultMapper;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.spevalfacade.bean.evaluation.Evaluation;
import com.yxt.spevalfacade.bean.standar.StandarReq;
import com.yxt.talent.rv.application.activity.RvAomActivityService;
import com.yxt.talent.rv.application.authprj.dto.ActvInfoDto;
import com.yxt.talent.rv.application.authprj.dto.AuthRepeatDto;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeForAuthPrjEnum;
import com.yxt.talent.rv.controller.facade.viewobj.AuthPrjDTO;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserHistoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserIndicatorMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserHistoryPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserPO;
import com.yxt.talent.rv.infrastructure.service.remote.OteAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class AuthPrjService {
    private final AuthprjMapper authprjMapper;
    private final AuthPrjTodoAndMsgService authPrjTodoService;
    private final ActivityRepeatService activityRepeatService;
    private final ActivityService activityService;
    private final RvAomActivityService aomActivityService;
    private final SpevalAclService spevalAclService;
    private final AuthprjResultUserMapper authprjResultUserMapper;
    private final AuthprjResultUserHistoryMapper authprjResultUserHistoryMapper;
    private final OteAclService oteAclService;
    private final RocketMqAclSender rocketMqAclSender;
    private final BaseActivityResultMapper baseActivityResultMapper;
    private final AuthprjResultUserIndicatorMapper authprjResultUserIndicatorMapper;

    /**
     * 校验是否可以重学，项目级校验
     *
     * @param orgId
     * @param authPrjId
     * @return
     */
    public AuthRepeatDto canReStartPrj(String orgId, String authPrjId) {
        AuthprjPO authprjPO = authprjMapper.selectByIdAndOrg(orgId, authPrjId);
        if (authprjPO == null) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_NOT_EXIST);
        }
        AtomicBoolean canRestart = new AtomicBoolean(false); // 默认不可以重学
        AtomicBoolean haveCanNotRestartTask = new AtomicBoolean(false); // 是否有不可重学的任务
        List<String> canNotStartTaskNames = Lists.newArrayList();
        dealTask(orgId, authprjPO.getAomPrjId(), (actvInfoDto) -> {
            canRestart.set(true);
        }, (actvInfoDto) -> {
            canNotStartTaskNames.add(actvInfoDto.getRefName());
            haveCanNotRestartTask.set(true);
        });
        return AuthRepeatDto.builder()
            .canRestart(canRestart.get())
            .haveCanNotRestartTask(haveCanNotRestartTask.get())
            .canNotStartTaskNames(canNotStartTaskNames)
            .build();
    }

    /**
     * 指派重学
     *
     * @param authPrjId
     * @param userIds
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void reStartPrj(String orgId, String opUserId, String authPrjId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        // 结果数据
        List<AuthprjResultUserPO> oldUserResultList = authprjResultUserMapper.selectByAuthprjIdAndUserIds(orgId, authPrjId, new ArrayList<>(userIds));

        Activity activity = getActivity(orgId, authPrjId);
        AtomicBoolean canRestart = new AtomicBoolean(false); // 默认不可以重学

        // 6.4 查询学员任务的完成情况，只有任务进度是已完成的，才可以重学
        List<BaseActivityResult> userResults = baseActivityResultMapper.getByActvIdsAndUserIds(orgId, List.of(activity.getId()), userIds);
        if (CollectionUtils.isEmpty(userResults)){
            log.info("当前用户中，不存在用户完成过任务，{}, {}",activity.getId(), BeanHelper.bean2Json(userIds, JsonInclude.Include.NON_NULL));
            return;
        }

        // userId + 任务Id，对应的结果数据
        Map<Pair<String, Long>, BaseActivityResult> userTaskResultMap = userResults.stream()
            .collect(Collectors.toMap(e -> Pair.of(e.getUserId(), e.getItemId()), Function.identity(), (v1, v2) -> v1));

        // 记录某个活动，可以进行指派重学的用户
        Map<Pair<String,String>, List<String>> taskUserMap = new ConcurrentHashMap<>();

        // 可以进行指派重新的用户
        Set<String> canRestartUsers = new HashSet<>();

        // 各个活动重学
        dealTask(orgId, activity.getId(), (actvInfoDto) -> {
            canRestart.set(true);

            for (String userId : userIds){

                BaseActivityResult userActivityResult = userTaskResultMap.get(Pair.of(userId, actvInfoDto.getId()));
                if (userActivityResult != null && userActivityResult.getResultStatus() > 0){
                    log.info("用户已完成当前任务，可进行指派重学:{}, 活动信息:{}", userId, BeanHelper.bean2Json(actvInfoDto, JsonInclude.Include.NON_NULL));
                    List<String> uids = taskUserMap.getOrDefault(Pair.of(actvInfoDto.getRefId(), actvInfoDto.getRegId()), new ArrayList<>());
                    uids.add(userId);
                    taskUserMap.put(Pair.of(actvInfoDto.getRefId(), actvInfoDto.getRegId()), uids);
                }

                //            if (actvInfoDto.getRegId().equals(UacdTypeForAuthPrjEnum.ACTV_APPRAISAL.getRegId())) {
                //                BatchOperationUtil.batchExecute(userIds, 300, uids -> {
                //                    // 鉴定
                //                    ResetTaskResultMqBean resultMQ = new ResetTaskResultMqBean();
                //                    resultMQ.setOrgId(orgId);
                //                    resultMQ.setProjectId(activity.getId());
                //                    resultMQ.setUserIds(uids);
                //                    resultMQ.setOpUserId(opUserId);
                //                    resultMQ.setTargetId(actvInfoDto.getRefId());
                //                    resultMQ.setBatchId(YxtIdWorker.getId());
                //                    rocketMqAclSender.send(MQConstant.ACTV_APPRAISAL_RESET_RESULT, BeanHelper.bean2Json(resultMQ, JsonInclude.Include.NON_NULL));
                //                    log.info("重复指派发送MQ成功:{}", JSON.toJSONString(resultMQ));
                //                });
                //            }
            }
        }, null);

        if (!canRestart.get()){
            log.info("未找到可以指派重学的任务，不进行指派重学，{}",activity.getId());
            return;
        }

        if (taskUserMap.isEmpty()){
            log.info("无可进行指派重新的用户+任务，不进行指派重学，{}",activity.getId());
            return;
        }

        for (Map.Entry<Pair<String, String>, List<String>> entry : taskUserMap.entrySet()){
            // 各活动指派重学
            String refId = entry.getKey().getLeft();
            String regId = entry.getKey().getRight();
            List<String> uids = entry.getValue();
            if (CollectionUtils.isEmpty(uids)){
                log.info("该任务下无可进行指派重新的用户，不进行指派重学, {}:{}", refId, regId);
                continue;
            }
            if (regId.equals(UacdTypeForAuthPrjEnum.ACTV_EVAL.getRegId())) {
                // 测评活动重学
                spevalAclService.clearUserRecord(orgId, refId, uids);

            }
            if (regId.equals(UacdTypeForAuthPrjEnum.ACTV_EXAM.getRegId())) {
                // 考试
                oteAclService.clearUserRecord(orgId, refId, uids, opUserId);
            }
            canRestartUsers.addAll(uids);
        }

        if (canRestartUsers.isEmpty()){
            log.info("无可进行指派重新的用户，不进行指派重学，{}",activity.getId());
            return;
        }

        log.info("重置任务成功，活动:{}, 用户:{}", activity.getId(), BeanHelper.bean2Json(canRestartUsers, JsonInclude.Include.NON_NULL));
        // 6.4 清空指标数据
        authprjResultUserIndicatorMapper.clearUserRecord(orgId, authPrjId, new ArrayList<>(canRestartUsers));

        // 6.4 指派重学， 是否发消息
//        RepeatReq repeatReq = new RepeatReq();
//        repeatReq.setOrgId(orgId);
//        repeatReq.setRegType(UacdTypeForAuthPrjEnum.PRJ_AUTH.getRegId());
//        repeatReq.setActvId(activity.getId());
//        repeatReq.setUserIds(new ArrayList<>(canRestartUsers));
//        activityRepeatService.assessmentRepeat(orgId, repeatReq);


        if (CollectionUtils.isNotEmpty(oldUserResultList)){
            List<AuthprjResultUserHistoryPO> userHistoryPOS = new ArrayList<>();
            List<AuthprjResultUserPO> userResultList = new ArrayList<>();
            for (AuthprjResultUserPO userResult : oldUserResultList) {
                if (canRestartUsers.contains(userResult.getUserId())){
                    // 更新用户结果表，标记为重学中
                    AuthprjResultUserHistoryPO authprjResultUserHistoryPO = new AuthprjResultUserHistoryPO();
                    BeanHelper.copyProperties(userResult, authprjResultUserHistoryPO);
                    authprjResultUserHistoryPO.setId(ApiUtil.getUuid());
                    authprjResultUserHistoryPO.setUpdateTime(LocalDateTime.now());
                    authprjResultUserHistoryPO.setUpdateUserId(opUserId);
                    userHistoryPOS.add(authprjResultUserHistoryPO);
                    userResult.setUpdateTime(LocalDateTime.now());
                    userResult.setUpdateUserId(opUserId);
                    userResult.setLevelId("");
                    userResult.setScoreValue(null);
                    userResult.setTaskCompleted(0);
                    userResultList.add(userResult);
                }
            }
            if (CollectionUtils.isNotEmpty(userHistoryPOS)){
                authprjResultUserHistoryMapper.batchInsertOrUpdate(userHistoryPOS);
            }
            if (CollectionUtils.isNotEmpty(userResultList)){
                authprjResultUserMapper.batchInsertOrUpdate(userResultList);
            }
        }

        // 发送待办
        authPrjTodoService.addUserTodo(orgId, opUserId, authPrjId, new ArrayList<>(canRestartUsers));
    }

    /**
     * 项目下任务处理
     *
     * @param orgId
     * @param aomPrjId
     * @return
     */

    private void dealTask(
        String orgId, String aomPrjId, Consumer<ActvInfoDto> reStartConsumer, Consumer<ActvInfoDto> canNotStartConsumer) {
        List<ActivityArrangeItem4ActvMgr> list =
            aomActivityService.getActivityList(aomPrjId, UacdTypeForAuthPrjEnum.PRJ_AUTH.getRegId());
        log.info("活动列表，原始数据: {}", BeanHelper.bean2Json(list, JsonInclude.Include.NON_NULL));

        List<ActvInfoDto> evals = new ArrayList<>();
        for (ActivityArrangeItem4ActvMgr item : list) {
            if (!item.getRefRegId().equals(UacdTypeEnum.ACTV_SPEVAL.getRegId())) {
                reStartConsumer.accept(ActvInfoDto
                    .builder()
                    .id(item.getId())
                    .regId(item.getRefRegId())
                    .refId(item.getRefId())
                    .refName(item.getRefName())
                    .build());
                // 只要包含一个非测评的，就可以重学
            } else {
                evals.add(ActvInfoDto.builder()
                    .id(item.getId())
                    .regId(item.getRefRegId())
                    .refId(item.getRefId())
                    .refName(item.getRefName())
                    .build());;
            }
        }
        if (CollectionUtils.isNotEmpty(evals)) {
            StandarReq req = new StandarReq();
            req.setOrgId(orgId);
            List<String> evalIds = evals.stream().map(ActvInfoDto::getRefId).collect(Collectors.toList());
            req.setEvalIds(evalIds);
            List<Evaluation> evaluations = spevalAclService.getBatchEvaluation(req);
            if (CollectionUtils.isEmpty(evaluations)) {
                log.info("当前项目中，不存在测评活动，{}", BeanHelper.bean2Json(evalIds, JsonInclude.Include.NON_NULL));
                return;
            }
            Map<String, Evaluation> evalMap = evaluations.stream().collect(Collectors.toMap(Evaluation::getId, Function.identity(), (u, v) -> u));
            for (ActvInfoDto evalDto : evals) {
                Evaluation evaluation = evalMap.get(evalDto.getRefId());
                if (evaluation != null && evaluation.getDeleted() != 1) {
                    if (evaluation.getType() == 3 || evaluation.getAiEval() == 1) {
                        // 如果有AI测评或者倍智测评，则有不可重学的任务
                        if (canNotStartConsumer != null){
                            canNotStartConsumer.accept(evalDto);
                        }
                    } else {
                        // 其他测评，就可以重学
                        reStartConsumer.accept(evalDto);
                    }
                }
            }
        }

    }

    public List<AuthprjPO> findAuthPrjsByAomIds(String orgId, List<String> aomIds) {
        if (CollectionUtils.isEmpty(aomIds)) {
            return new ArrayList<>();
        }
        return authprjMapper.selectByAomIds(orgId, aomIds);
    }

    public Activity getActivity(String orgId, String authPrjId) {
        AuthprjPO authprjPO = authprjMapper.selectByIdAndOrg(orgId, authPrjId);
        Validate.isNotNull(authprjPO, ExceptionKeys.AUTHPRJ_NOT_EXIST);
        Activity activity = activityService.findById(orgId, authprjPO.getAomPrjId());
        return Validate.isNotNull(activity, ExceptionKeys.AUTHPRJ_NOT_EXIST);
    }

    public AuthPrjDTO getAuthInfoByAomId(String orgId, String aomId) {
        AuthprjPO authprjPO = authprjMapper.selectByAomPrjId(orgId, aomId);
        Validate.isNotNull(authprjPO, ExceptionKeys.AUTHPRJ_NOT_EXIST);
        Activity activity = activityService.findById(orgId, authprjPO.getAomPrjId());
        Validate.isNotNull(activity, ExceptionKeys.AUTHPRJ_NOT_EXIST);
        AuthPrjDTO authPrjDTO = new AuthPrjDTO();
        authPrjDTO.setAuthPrjId(authprjPO.getId());
        authPrjDTO.setAomId(activity.getId());
        authPrjDTO.setStatus(activity.getActvStatus());
        return authPrjDTO;
    }
}
