package com.yxt.talent.rv;

import com.yxt.talent.rv.application.authprj.AuthPrjTodoAndMsgService;
import com.yxt.talent.rv.infrastructure.common.constant.enums.UTreeEnum;
import com.yxt.ubiz.tree.extension.UTreeComponent;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 以启动spring容器的方式进行测试
 */
@Transactional
@SpringBootTest
@AutoConfigureMockMvc
@Rollback(value = true)
@ActiveProfiles("native")
public class AuthprjTodoTest extends BaseTest { // NOSONAR

    @Autowired
    private AuthPrjTodoAndMsgService authPrjTodoService;
    @Autowired
    private UTreeComponent uTreeComponent;

    @Test
    public void testInfo(){
//        String categoryId = uTreeComponent.getRootNodeId("ac0a267c-044e-4a28-b489-6be16e0ab788", UTreeEnum.AUTHPRJ_BASE.getTreeId());
//        System.out.println(categoryId);

        Map<String, String> categoryMap =
            uTreeComponent.getNodesName("ac0a267c-044e-4a28-b489-6be16e0ab788", UTreeEnum.AUTHPRJ_BASE.getTreeId(),Lists.newArrayList("1"));
        System.out.println(categoryMap);
//        authPrjTodoService.delTodo("ac0a267c-044e-4a28-b489-6be16e0ab788","00f5760d-f921-424f-adaa-6a94b812baab","501520ac-a683-4f91-901d-acae1eb711b2");
//        authPrjTodoService.createTodo("ac0a267c-044e-4a28-b489-6be16e0ab788","00f5760d-f921-424f-adaa-6a94b812baab","501520ac-a683-4f91-901d-acae1eb711b2");
    }


}
