<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.yxt.usdk.bom</groupId>
    <artifactId>usdk-bom-waf</artifactId>
    <version>1.2.7-jdk17</version>
  </parent>

  <groupId>com.yxt</groupId>
  <artifactId>sptalentrvapi</artifactId>
  <version>6.1.2-jdk17</version>
  <packaging>jar</packaging>

  <name>sptalentrvapi</name>
  <description>奇点-人才盘点项目</description>

  <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
    <root.basedir>${user.dir}</root.basedir>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <liquibase.db.host>************</liquibase.db.host>
    <liquibase.db.username>yxt</liquibase.db.username>
    <liquibase.db.password>afg)gppOs22k</liquibase.db.password>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    <mapstruct.version>1.5.5.Final</mapstruct.version>
    <json-lib.version>2.4</json-lib.version>
    <lombok.version>1.18.24</lombok.version>
    <modelhub-api.version>1.7.0</modelhub-api.version>

    <usdk.poi.version>4.1.2</usdk.poi.version>
    <usdk.easyexcel.version>3.0.1</usdk.easyexcel.version>

    <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
    <maven-antrun-plugin.version>3.1.0</maven-antrun-plugin.version>
    <aom.version>1.3.0-SNAPSHOT</aom.version>
  </properties>

  <repositories>
      <repository>
          <id>yxt-snapshots</id>
          <url>http://maven.yxt.com.cn/nexus/content/repositories/snapshots</url>
          <releases>
              <enabled>false</enabled>
          </releases>
          <snapshots>
              <enabled>true</enabled>
          </snapshots>
      </repository>
      <repository>
          <id>yxt-release</id>
          <url>http://maven.yxt.com.cn/nexus/content/groups/public</url>
          <releases>
              <enabled>true</enabled>
          </releases>
          <snapshots>
              <enabled>false</enabled>
          </snapshots>
      </repository>
  </repositories>
  <pluginRepositories>
      <pluginRepository>
          <id>yxt-release</id>
          <url>http://maven.yxt.com.cn/nexus/content/groups/public</url>
      </pluginRepository>
  </pluginRepositories>

    <dependencies>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sp-sdk</artifactId>
            <version>1.4.1-jdk17</version>
            <exclusions>
                <exclusion>
                    <artifactId>modelhub-api</artifactId>
                    <groupId>com.yxt.modelhub</groupId>
                </exclusion>
              <exclusion>
                <artifactId>waf-export</artifactId>
                <groupId>com.yxt</groupId>
              </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt.talent</groupId>
            <artifactId>ddd-spring-boot-starter</artifactId>
            <version>1.1.10-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>waf-idworker</artifactId>
            <version>2.2.4-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt.ubiz</groupId>
            <artifactId>ubiz-export</artifactId>
            <version>1.2.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sptalentsdapi-facade</artifactId>
            <version>6.3.1-jdk17</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-compress</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.19</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>waf-export</artifactId>
            <version>2.3.10-jdk17</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-devtools</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bcprov-jdk14</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bcmail-jdk14</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bcpkix-jdk14</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>waf-job</artifactId>
            <version>2.3.8-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt.usdk.components</groupId>
            <artifactId>usdk-components-rocketmq</artifactId>
            <version>2.1.5-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>waf-trace</artifactId>
            <version>2.4.2-jdk17</version>
        </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-core</artifactId>
        <version>10.1.5</version>
      </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>spevalapi-facade</artifactId>
            <version>6.4.1-jdk17-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>dmp-api</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>aom-project</artifactId>
            <version>${aom.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>miscapi-facade</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>aom-migr</artifactId>
            <version>1.0.0-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>aom-activity</artifactId>
            <version>${aom.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>miscapi-facade</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>bifrost-sdk-udp</artifactId>
            <version>1.0.6-jdk17</version>
        </dependency>
        <dependency>
          <groupId>com.yxt</groupId>
          <artifactId>oteapi-facade</artifactId>
          <version>6.2.1-jdk17</version>
          <exclusions>
            <exclusion>
              <artifactId>knife4j-micro-spring-boot-starter</artifactId>
              <groupId>com.github.xiaoymin</groupId>
            </exclusion>
            <exclusion>
              <artifactId>knife4j-spring-boot-starter</artifactId>
              <groupId>com.github.xiaoymin</groupId>
            </exclusion>
            <exclusion>
              <artifactId>waf-base</artifactId>
              <groupId>com.yxt</groupId>
            </exclusion>
          </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>udpapi-facade</artifactId>
            <version>2.9.10</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sptalentapi-facade</artifactId>
            <version>5.8.5-jdk17</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sptalentbk-facade</artifactId>
            <version>6.3-jdk17</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>sptalentrv-facade</artifactId>
            <version>6.4.1-jdk17-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>coreapi-facade</artifactId>
            <version>3.1.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>polestarapi-facade</artifactId>
            <version>2.5.1-jdk17</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
          <groupId>com.yxt</groupId>
          <artifactId>cerapi-facade</artifactId>
          <version>5.8.1-jdk17</version>
          <exclusions>
            <exclusion>
              <artifactId>knife4j-micro-spring-boot-starter</artifactId>
              <groupId>com.github.xiaoymin</groupId>
            </exclusion>
            <exclusion>
              <artifactId>knife4j-spring-boot-starter</artifactId>
              <groupId>com.github.xiaoymin</groupId>
            </exclusion>
            <exclusion>
              <artifactId>waf-base</artifactId>
              <groupId>com.yxt</groupId>
            </exclusion>
          </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>orginitapi-facade</artifactId>
            <version>2.6.9-jdk17</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>o2oapi-facade</artifactId>
            <version>5.6-jdk17</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>msg-facade</artifactId>
            <version>2.11.7-jdk17</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>globalapi-facade</artifactId>
            <version>2.5.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>spmodelapi-facade</artifactId>
            <version>1.3.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>knife4j-micro-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>knife4j-spring-boot-starter</artifactId>
                    <groupId>com.github.xiaoymin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.usdk.components</groupId>
            <artifactId>usdk-components-es6configure</artifactId>
            <version>1.0.0-jdk17</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>8.0.33</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>0.2.0</version>
        </dependency>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>${json-lib.version}</version>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.ubiz</groupId>
            <artifactId>ubiz-tree</artifactId>
            <version>1.3.5</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>ikit-i18n</artifactId>
            <version>1.0.9</version>
            <exclusions>
                <exclusion>
                    <artifactId>waf-base</artifactId>
                    <groupId>com.yxt</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt.modelhub</groupId>
            <artifactId>modelhub-api</artifactId>
            <version>${modelhub-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${usdk.easyexcel.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    
    


  <build>
    <pluginManagement>
      <plugins>
        <!-- 此插件可以用来批量管理子模块版本 -->
        <!-- 执行mvn versions:set -DnewVersion=xxx 即可批量修改module的版本-->
        <!-- 执行mvn versions:display-dependency- updates 项目依赖有哪些可用的更新 -->
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>versions-maven-plugin</artifactId>
          <version>2.11.0</version>
          <configuration>
            <generateBackupPoms>false</generateBackupPoms>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${maven-antrun-plugin.version}</version>
          <executions>
            <execution>
              <id>copy</id>
              <phase>package</phase>
              <configuration>
                <tasks>
                  <copy todir="../target/">
                    <!-- project.build.directory表示各个模块的target目录 -->
                    <fileset dir="${project.build.directory}">
                      <!-- 需要复制的jar包文件名称 -->
                      <include name="*.jar"/>
                    </fileset>
                  </copy>

                </tasks>
              </configuration>
              <goals>
                <goal>run</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>17</source>
          <target>17</target>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>0.2.0</version>
            </path>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${mapstruct.version}</version>
            </path>
            <path>
              <groupId>org.springframework.boot</groupId>
              <artifactId>spring-boot-configuration-processor</artifactId>
              <version>3.0.2</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>versions-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-maven-plugin</artifactId>
        <version>4.26.0</version>
        <configuration>
          <driver>com.mysql.cj.jdbc.Driver</driver>
          <url>jdbc:mysql://${liquibase.db.host}:3306/sprv?useUnicode=true&amp;characterEncoding=UTF-8</url>
          <username>${liquibase.db.username}</username>
          <password>${liquibase.db.password}</password>
          <outputDefaultSchema>true</outputDefaultSchema>
          <changeSetAuthor>liquibase</changeSetAuthor>
          <diffTypes>tables,indexes,columns,foreignkeys,primaryKeys,uniqueconstraints</diffTypes>
          <changeLogFile>src/main/resources/db/changelog/db.changelog-master.yaml</changeLogFile>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
