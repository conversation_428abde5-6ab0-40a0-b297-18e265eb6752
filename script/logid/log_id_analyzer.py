#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志ID分析工具

用于分析Java项目中的日志ID使用情况，检测重复的日志ID，
找到当前最大的日志编号，并生成分析报告。

作者: AI Assistant
日期: 2025-08-07
"""

import argparse
import os
import re
import sys
from collections import defaultdict
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Tuple, Set


@dataclass
class LogEntry:
    """日志条目数据类"""
    log_id: str
    file_path: str
    line_number: int
    line_content: str


class LogIdAnalyzer:
    """日志ID分析器"""
    
    def __init__(self, project_root: str):
        """
        初始化分析器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root)
        self.log_entries: List[LogEntry] = []
        
        # 正则表达式模式
        # 匹配单行日志ID: log.info("LOG12345:消息内容", ...)
        self.single_line_pattern = re.compile(
            r'log\.(?:trace|debug|info|warn|error)\s*\(\s*"(LOG\d+):', 
            re.IGNORECASE
        )
        
        # 匹配多行日志的开始: log.info(
        self.multi_line_start_pattern = re.compile(
            r'^\s*log\.(?:trace|debug|info|warn|error)\s*\(\s*$', 
            re.IGNORECASE
        )
        
        # 匹配多行日志中的LOG ID: "LOG12345:消息内容"
        self.multi_line_logid_pattern = re.compile(r'"(LOG\d+):', re.IGNORECASE)
        
        # 匹配LOG ID的数字部分
        self.logid_number_pattern = re.compile(r'LOG(\d+)', re.IGNORECASE)
    
    def scan_java_files(self) -> List[Path]:
        """
        扫描项目目录中的所有Java文件
        
        Returns:
            Java文件路径列表
        """
        java_files = []
        
        # 递归查找所有.java文件
        for java_file in self.project_root.rglob("*.java"):
            # 排除target目录、构建目录和备份目录
            if any(part in ['target', 'build', '.git', 'node_modules'] or part.startswith('backup_')
                   for part in java_file.parts):
                continue
            java_files.append(java_file)
        
        return java_files
    
    def extract_log_ids_from_file(self, file_path: Path) -> List[LogEntry]:
        """
        从单个Java文件中提取日志ID
        
        Args:
            file_path: Java文件路径
            
        Returns:
            该文件中的日志条目列表
        """
        entries = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            is_multi_line_log = False
            
            for line_num, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                # 检查单行日志ID
                single_match = self.single_line_pattern.search(line)
                if single_match:
                    log_id = single_match.group(1)
                    entries.append(LogEntry(
                        log_id=log_id,
                        file_path=str(file_path.relative_to(self.project_root)),
                        line_number=line_num,
                        line_content=line_stripped
                    ))
                    continue
                
                # 检查多行日志的开始
                if self.multi_line_start_pattern.match(line_stripped):
                    is_multi_line_log = True
                    continue
                
                # 在多行日志中查找LOG ID
                if is_multi_line_log:
                    multi_match = self.multi_line_logid_pattern.search(line)
                    if multi_match:
                        log_id = multi_match.group(1)
                        entries.append(LogEntry(
                            log_id=log_id,
                            file_path=str(file_path.relative_to(self.project_root)),
                            line_number=line_num,
                            line_content=line_stripped
                        ))
                        is_multi_line_log = False
                    elif line_stripped.endswith(');') or line_stripped.endswith(')'):
                        # 多行日志结束
                        is_multi_line_log = False
        
        except Exception as e:
            print(f"警告: 读取文件 {file_path} 时出错: {e}")
        
        return entries
    
    def analyze_project(self) -> None:
        """分析整个项目"""
        print("🔍 开始扫描Java文件...")
        java_files = self.scan_java_files()
        print(f"📁 找到 {len(java_files)} 个Java文件")
        
        print("🔍 提取日志ID...")
        for file_path in java_files:
            entries = self.extract_log_ids_from_file(file_path)
            self.log_entries.extend(entries)
        
        print(f"📊 总共找到 {len(self.log_entries)} 个日志条目")
    
    def find_duplicate_log_ids(self) -> Dict[str, List[LogEntry]]:
        """
        查找重复的日志ID
        
        Returns:
            重复日志ID的字典，键为日志ID，值为使用该ID的条目列表
        """
        log_id_map = defaultdict(list)
        
        for entry in self.log_entries:
            log_id_map[entry.log_id].append(entry)
        
        # 只返回出现次数大于1的日志ID
        duplicates = {log_id: entries for log_id, entries in log_id_map.items() 
                     if len(entries) > 1}
        
        return duplicates
    
    def find_max_log_id(self) -> int:
        """
        查找当前使用的最大日志ID编号
        
        Returns:
            最大的日志ID数字
        """
        max_id = 0
        
        for entry in self.log_entries:
            match = self.logid_number_pattern.search(entry.log_id)
            if match:
                log_number = int(match.group(1))
                max_id = max(max_id, log_number)
        
        return max_id
    
    def get_all_log_ids(self) -> Set[str]:
        """获取所有使用的日志ID"""
        return {entry.log_id for entry in self.log_entries}
    
    def generate_report(self) -> str:
        """
        生成分析报告
        
        Returns:
            格式化的分析报告字符串
        """
        duplicates = self.find_duplicate_log_ids()
        max_log_id = self.find_max_log_id()
        all_log_ids = self.get_all_log_ids()
        
        report = []
        report.append("=" * 80)
        report.append("📋 日志ID使用情况分析报告")
        report.append("=" * 80)
        report.append("")
        
        # 基本统计信息
        report.append("📊 基本统计信息:")
        report.append(f"   • 总日志条目数: {len(self.log_entries)}")
        report.append(f"   • 唯一日志ID数: {len(all_log_ids)}")
        report.append(f"   • 重复日志ID数: {len(duplicates)}")
        report.append(f"   • 当前最大日志ID: LOG{max_log_id:05d}")
        report.append(f"   • 建议下一个日志ID: LOG{max_log_id + 1:05d}")
        report.append("")
        
        # 重复日志ID详情
        if duplicates:
            report.append("⚠️  重复日志ID详情:")
            report.append("-" * 60)
            
            for log_id, entries in sorted(duplicates.items()):
                report.append(f"\n🔴 {log_id} (出现 {len(entries)} 次):")
                for entry in entries:
                    report.append(f"   📍 {entry.file_path}:{entry.line_number}")
                    report.append(f"      {entry.line_content[:100]}{'...' if len(entry.line_content) > 100 else ''}")
        else:
            report.append("✅ 未发现重复的日志ID")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_report_to_file(self, output_file: str = None) -> str:
        """
        将报告保存到文件
        
        Args:
            output_file: 输出文件路径，如果为None则使用默认路径
            
        Returns:
            实际保存的文件路径
        """
        if output_file is None:
            output_file = self.project_root / "script" / "logid" / "log_id_analysis_report.txt"
        
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        report_content = self.generate_report()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        return str(output_path)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Java项目日志ID分析工具')
    parser.add_argument(
        '--project-root', 
        type=str, 
        default='.',
        help='项目根目录路径 (默认: 当前目录)'
    )
    parser.add_argument(
        '--output', 
        type=str, 
        help='报告输出文件路径 (默认: script/logid/log_id_analysis_report.txt)'
    )
    parser.add_argument(
        '--no-save', 
        action='store_true',
        help='不保存报告到文件，只在控制台显示'
    )
    
    args = parser.parse_args()
    
    # 获取项目根目录
    project_root = Path(args.project_root).resolve()
    
    if not project_root.exists():
        print(f"❌ 错误: 项目目录不存在: {project_root}")
        sys.exit(1)
    
    print(f"🚀 开始分析项目: {project_root}")
    
    # 创建分析器并执行分析
    analyzer = LogIdAnalyzer(str(project_root))
    analyzer.analyze_project()
    
    # 生成并显示报告
    report = analyzer.generate_report()
    print(report)
    
    # 保存报告到文件
    if not args.no_save:
        output_file = analyzer.save_report_to_file(args.output)
        print(f"\n💾 报告已保存到: {output_file}")


if __name__ == "__main__":
    main()
