#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动修复重复日志ID工具

根据分析结果，自动将重复的日志ID修改为新的唯一ID。
新ID从当前最大ID开始，每次递增10。

使用方法:
    python fix_duplicate_log_ids.py
    python fix_duplicate_log_ids.py --dry-run  # 预览模式，不实际修改文件
    python fix_duplicate_log_ids.py --increment 20  # 自定义递增步长
"""

import argparse
import os
import re
import sys
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Tuple


class LogIdFixer:
    """日志ID修复器"""
    
    def __init__(self, project_root: str, increment: int = 10, dry_run: bool = False):
        """
        初始化修复器
        
        Args:
            project_root: 项目根目录
            increment: ID递增步长，默认10
            dry_run: 是否为预览模式，不实际修改文件
        """
        self.project_root = Path(project_root)
        self.increment = increment
        self.dry_run = dry_run
        self.log_entries = []
        
        # 正则表达式模式
        self.single_line_pattern = re.compile(
            r'log\.(?:trace|debug|info|warn|error)\s*\(\s*"(LOG\d+):', 
            re.IGNORECASE
        )
        self.multi_line_logid_pattern = re.compile(r'"(LOG\d+):', re.IGNORECASE)
        self.logid_number_pattern = re.compile(r'LOG(\d+)', re.IGNORECASE)
    
    def scan_and_analyze(self):
        """扫描并分析项目中的日志ID"""
        print("🔍 扫描Java文件...")
        java_files = self._find_java_files()
        print(f"📁 找到 {len(java_files)} 个Java文件")
        
        print("🔍 提取日志ID...")
        for file_path in java_files:
            entries = self._extract_log_ids_from_file(file_path)
            self.log_entries.extend(entries)
        
        print(f"📊 总共找到 {len(self.log_entries)} 个日志条目")
    
    def _find_java_files(self) -> List[Path]:
        """查找所有Java文件"""
        java_files = []
        for java_file in self.project_root.rglob("*.java"):
            if any(part in ['target', 'build', '.git'] or part.startswith('backup_')
                   for part in java_file.parts):
                continue
            java_files.append(java_file)
        return java_files
    
    def _extract_log_ids_from_file(self, file_path: Path) -> List[Dict]:
        """从文件中提取日志ID"""
        entries = []
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            is_multi_line_log = False
            
            for line_num, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                # 检查单行日志ID
                single_match = self.single_line_pattern.search(line)
                if single_match:
                    log_id = single_match.group(1)
                    entries.append({
                        'log_id': log_id,
                        'file_path': file_path,
                        'line_number': line_num,
                        'line_content': line,
                        'original_line': line
                    })
                    continue
                
                # 检查多行日志中的LOG ID
                if '"LOG' in line:
                    multi_match = self.multi_line_logid_pattern.search(line)
                    if multi_match:
                        log_id = multi_match.group(1)
                        entries.append({
                            'log_id': log_id,
                            'file_path': file_path,
                            'line_number': line_num,
                            'line_content': line,
                            'original_line': line
                        })
        
        except Exception as e:
            print(f"⚠️  读取文件出错 {file_path}: {e}")
        
        return entries
    
    def find_duplicates_and_max_id(self) -> Tuple[Dict, int]:
        """查找重复的日志ID和最大ID"""
        log_id_map = defaultdict(list)
        max_id = 0
        
        for entry in self.log_entries:
            log_id_map[entry['log_id']].append(entry)
            
            # 提取数字部分
            match = self.logid_number_pattern.search(entry['log_id'])
            if match:
                log_number = int(match.group(1))
                max_id = max(max_id, log_number)
        
        # 只返回重复的日志ID
        duplicates = {log_id: entries for log_id, entries in log_id_map.items() 
                     if len(entries) > 1}
        
        return duplicates, max_id
    
    def fix_duplicates(self):
        """修复重复的日志ID"""
        duplicates, max_id = self.find_duplicates_and_max_id()
        
        if not duplicates:
            print("✅ 未发现重复的日志ID")
            return
        
        print(f"⚠️  发现 {len(duplicates)} 个重复的日志ID")
        
        # 生成新的日志ID映射
        next_id = max_id + self.increment
        id_mapping = {}
        
        print("\n📋 重复日志ID修复计划:")
        print("=" * 60)
        
        for log_id, entries in sorted(duplicates.items()):
            print(f"\n🔴 {log_id} (出现 {len(entries)} 次):")
            
            # 第一个保持不变，后续的分配新ID
            for i, entry in enumerate(entries):
                if i == 0:
                    print(f"   ✅ 保持原ID: {entry['file_path'].relative_to(self.project_root)}:{entry['line_number']}")
                else:
                    new_log_id = f"LOG{next_id:05d}"
                    id_mapping[(entry['file_path'], entry['line_number'], log_id)] = new_log_id
                    print(f"   🔄 {log_id} → {new_log_id}: {entry['file_path'].relative_to(self.project_root)}:{entry['line_number']}")
                    next_id += self.increment
        
        print(f"\n📈 下一个可用日志ID: LOG{next_id:05d}")
        
        if self.dry_run:
            print("\n🔍 预览模式 - 未实际修改文件")
            return
        
        # 执行文件修改
        print("\n🔧 开始修改文件...")
        files_to_modify = {}
        
        # 按文件分组需要修改的内容
        for (file_path, line_number, old_id), new_id in id_mapping.items():
            if file_path not in files_to_modify:
                files_to_modify[file_path] = []
            files_to_modify[file_path].append((line_number, old_id, new_id))
        
        # 修改每个文件
        for file_path, modifications in files_to_modify.items():
            self._modify_file(file_path, modifications)
        
        print(f"\n✅ 修复完成！共修改了 {len(files_to_modify)} 个文件")
    
    def _modify_file(self, file_path: Path, modifications: List[Tuple[int, str, str]]):
        """修改单个文件"""
        print(f"📝 修改文件: {file_path.relative_to(self.project_root)}")
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 按行号排序修改项（从大到小，避免行号变化影响）
            modifications.sort(key=lambda x: x[0], reverse=True)
            
            # 执行修改
            for line_number, old_id, new_id in modifications:
                if line_number <= len(lines):
                    line = lines[line_number - 1]  # 转换为0基索引
                    # 替换日志ID
                    new_line = line.replace(old_id, new_id)
                    lines[line_number - 1] = new_line
                    print(f"   第{line_number}行: {old_id} → {new_id}")
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
        
        except Exception as e:
            print(f"❌ 修改文件失败 {file_path}: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='自动修复重复日志ID工具')
    parser.add_argument(
        '--project-root', 
        type=str, 
        default='.',
        help='项目根目录路径 (默认: 当前目录)'
    )
    parser.add_argument(
        '--increment', 
        type=int, 
        default=10,
        help='ID递增步长 (默认: 10)'
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help='预览模式，不实际修改文件'
    )
    
    args = parser.parse_args()
    
    # 获取项目根目录
    project_root = Path(args.project_root).resolve()
    
    if not project_root.exists():
        print(f"❌ 错误: 项目目录不存在: {project_root}")
        sys.exit(1)
    
    print(f"🚀 开始修复重复日志ID: {project_root}")
    if args.dry_run:
        print("🔍 运行模式: 预览模式（不会实际修改文件）")
    else:
        print("⚠️  运行模式: 实际修改模式")
        response = input("确认要修改文件吗？(y/N): ")
        if response.lower() != 'y':
            print("❌ 操作已取消")
            sys.exit(0)
    
    # 创建修复器并执行修复
    fixer = LogIdFixer(str(project_root), args.increment, args.dry_run)
    fixer.scan_and_analyze()
    fixer.fix_duplicates()


if __name__ == "__main__":
    main()
