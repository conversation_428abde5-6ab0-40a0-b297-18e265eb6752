# 日志ID分析工具

这个工具集用于分析Java项目中的日志ID使用情况，检测重复的日志ID，并提供修复建议。

## 工具说明

### 1. log_id_analyzer.py - 完整分析工具

功能最全面的日志ID分析工具，提供详细的分析报告。

**主要功能:**
- 递归扫描项目中的所有Java文件
- 提取所有日志ID（支持单行和多行日志格式）
- 检测重复的日志ID并显示具体位置
- 找到当前最大的日志编号
- 生成详细的分析报告
- 支持将报告保存到文件

**使用方法:**
```bash
# 分析当前目录的项目
python log_id_analyzer.py

# 分析指定目录的项目
python log_id_analyzer.py --project-root /path/to/project

# 指定报告输出文件
python log_id_analyzer.py --output /path/to/report.txt

# 只在控制台显示，不保存文件
python log_id_analyzer.py --no-save
```

### 2. quick_duplicate_check.py - 快速检查工具

轻量级的重复日志ID检查工具，适用于日常开发中的快速检查。

**主要功能:**
- 快速扫描重复的日志ID
- 显示重复ID的具体位置
- 显示当前最大日志ID和建议的下一个ID
- 如果发现重复ID，退出码为1（适合CI/CD集成）

**使用方法:**
```bash
# 检查当前目录
python quick_duplicate_check.py

# 检查指定目录
python quick_duplicate_check.py --project-root /path/to/project
```

## 支持的日志格式

工具支持以下日志格式的ID提取：

### 单行格式
```java
log.info("LOG12345:消息内容", param1, param2);
log.error("LOG67890:错误信息", exception);
```

### 多行格式
```java
log.info(
    "LOG12345:消息内容, param1={}, param2={}", 
    param1, param2
);
```

### 支持的日志级别
- `log.trace`
- `log.debug`
- `log.info`
- `log.warn`
- `log.error`

## 输出示例

### 完整分析报告示例
```
================================================================================
📋 日志ID使用情况分析报告
================================================================================

📊 基本统计信息:
   • 总日志条目数: 156
   • 唯一日志ID数: 154
   • 重复日志ID数: 1
   • 当前最大日志ID: LOG64630
   • 建议下一个日志ID: LOG64631

⚠️  重复日志ID详情:
------------------------------------------------------------

🔴 LOG41096 (出现 2 次):
   📍 src/main/java/com/example/ServiceA.java:45
      log.info("LOG41096:消息版本已过期，跳过处理: userId={}", userId);
   📍 src/main/java/com/example/ServiceB.java:123
      log.warn("LOG41096:处理失败，重试中: taskId={}", taskId);

================================================================================
```

### 快速检查输出示例
```
🔍 扫描Java文件...
📁 找到 89 个Java文件
🔍 提取日志ID...
📊 找到 156 个日志条目

⚠️  发现 1 个重复的日志ID:
============================================================

🔴 LOG41096 (重复 2 次):
   📍 src/main/java/com/example/ServiceA.java:45
   📍 src/main/java/com/example/ServiceB.java:123

============================================================
❌ 请修复上述重复的日志ID
```

## 集成到开发流程

### 1. 预提交检查
在Git预提交钩子中使用快速检查工具：

```bash
#!/bin/bash
# .git/hooks/pre-commit
python script/logid/quick_duplicate_check.py
if [ $? -ne 0 ]; then
    echo "❌ 提交被阻止：发现重复的日志ID，请先修复"
    exit 1
fi
```

### 2. CI/CD集成
在CI/CD流水线中添加日志ID检查步骤：

```yaml
# GitHub Actions 示例
- name: Check Duplicate Log IDs
  run: |
    python script/logid/quick_duplicate_check.py
    if [ $? -ne 0 ]; then
      echo "发现重复日志ID，构建失败"
      exit 1
    fi
```

### 3. 定期分析
建议定期运行完整分析工具，生成详细报告：

```bash
# 每周运行一次完整分析
python script/logid/log_id_analyzer.py --output reports/log_id_analysis_$(date +%Y%m%d).txt
```

## 注意事项

1. **编码格式**: 工具会尝试以UTF-8编码读取文件，如果遇到编码问题会忽略错误继续处理
2. **文件过滤**: 自动排除`target`、`build`、`.git`等构建和版本控制目录
3. **性能**: 大型项目可能需要几秒钟的扫描时间，这是正常的
4. **日志ID格式**: 工具严格按照`LOG\d+:`格式匹配，确保日志ID的一致性

## 故障排除

### 常见问题

**Q: 工具报告找不到任何日志ID**
A: 检查项目路径是否正确，确保项目中确实存在包含日志ID的Java文件

**Q: 某些日志ID没有被检测到**
A: 确保日志ID严格遵循`LOG\d+:`格式，注意大小写和冒号

**Q: 工具运行很慢**
A: 对于大型项目，可以考虑使用快速检查工具，或者排除不必要的目录

如有其他问题，请检查工具的输出信息或联系开发团队。
