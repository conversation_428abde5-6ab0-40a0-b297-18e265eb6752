#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速重复日志ID检查工具

快速扫描项目中的重复日志ID，适用于日常开发中的快速检查。

使用方法:
    python quick_duplicate_check.py
    python quick_duplicate_check.py --project-root /path/to/project
"""

import argparse
import os
import re
import sys
from collections import defaultdict
from pathlib import Path


def find_java_files(project_root: Path) -> list:
    """查找所有Java文件"""
    java_files = []
    for java_file in project_root.rglob("*.java"):
        # 排除构建目录和备份目录
        if any(part in ['target', 'build', '.git', 'backup_'] or part.startswith('backup_')
               for part in java_file.parts):
            continue
        java_files.append(java_file)
    return java_files


def extract_log_ids(file_path: Path) -> list:
    """从文件中提取日志ID"""
    log_entries = []
    
    # 正则表达式匹配 LOG\d+:
    pattern = re.compile(r'(LOG\d+):', re.IGNORECASE)
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                matches = pattern.findall(line)
                for log_id in matches:
                    log_entries.append({
                        'log_id': log_id.upper(),
                        'file': str(file_path.relative_to(file_path.parents[len(file_path.parts) - file_path.parts.index('src')])),
                        'line': line_num,
                        'content': line.strip()
                    })
    except Exception as e:
        print(f"⚠️  读取文件出错 {file_path}: {e}")
    
    return log_entries


def main():
    parser = argparse.ArgumentParser(description='快速重复日志ID检查')
    parser.add_argument('--project-root', type=str, default='.', help='项目根目录')
    args = parser.parse_args()
    
    project_root = Path(args.project_root).resolve()
    
    print("🔍 扫描Java文件...")
    java_files = find_java_files(project_root)
    print(f"📁 找到 {len(java_files)} 个Java文件")
    
    print("🔍 提取日志ID...")
    all_log_entries = []
    for file_path in java_files:
        entries = extract_log_ids(file_path)
        all_log_entries.extend(entries)
    
    print(f"📊 找到 {len(all_log_entries)} 个日志条目")
    
    # 按日志ID分组
    log_id_groups = defaultdict(list)
    for entry in all_log_entries:
        log_id_groups[entry['log_id']].append(entry)
    
    # 查找重复的日志ID
    duplicates = {log_id: entries for log_id, entries in log_id_groups.items() 
                 if len(entries) > 1}
    
    if duplicates:
        print(f"\n⚠️  发现 {len(duplicates)} 个重复的日志ID:")
        print("=" * 60)
        
        for log_id, entries in sorted(duplicates.items()):
            print(f"\n🔴 {log_id} (重复 {len(entries)} 次):")
            for entry in entries:
                print(f"   📍 {entry['file']}:{entry['line']}")
        
        print("\n" + "=" * 60)
        print("❌ 请修复上述重复的日志ID")
        sys.exit(1)
    else:
        print("\n✅ 未发现重复的日志ID")
    
    # 显示最大日志ID
    max_id = 0
    for log_id in log_id_groups.keys():
        match = re.search(r'LOG(\d+)', log_id)
        if match:
            max_id = max(max_id, int(match.group(1)))
    
    print(f"📈 当前最大日志ID: LOG{max_id:05d}")
    print(f"💡 建议下一个日志ID: LOG{max_id + 1:05d}")


if __name__ == "__main__":
    main()
